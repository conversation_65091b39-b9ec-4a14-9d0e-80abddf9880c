import { ref, computed, watch, onUnmounted } from "vue";
import * as d3 from "d3";
import type { Coordinate } from "./useLottoData";

// 图层配置接口
export interface LayerConfig {
  showGrid: boolean;
  showPositiveDiagonal: boolean;
  showNegativeDiagonal: boolean;
  showCoordinateInfo: boolean;
  showTriangleCalculation: boolean;
  showLineDrawing: boolean;
  enableLineDrawing: boolean; // 启用连线绘制功能
}

// 可视化配置接口
export interface VisualizationConfig {
  width: number;
  height: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

// 图层管理器接口
export interface LayerManager {
  render(): void;
  destroy(): void;
  update(data: any): void;
  toggle(visible: boolean): void;
}

// 事件类型
export interface VisualizationEvents {
  onPointHover?: (point: Coordinate | null) => void;
  onPointClick?: (point: Coordinate) => void;
  onTriangleUpdate?: (
    a: Coordinate,
    b: Coordinate,
    c: Coordinate,
    isIsosceles: boolean
  ) => void;
}

/**
 * D3可视化核心组合函数
 * 统一管理所有图层和交互
 */
export function useD3Visualization(
  container: HTMLElement,
  config: VisualizationConfig,
  events?: VisualizationEvents
) {
  // 核心状态
  const isInitialized = ref(false);
  const svg = ref<d3.Selection<SVGSVGElement, unknown, null, undefined> | null>(
    null
  );
  const plotArea = ref<d3.Selection<
    SVGGElement,
    unknown,
    null,
    undefined
  > | null>(null);
  const xScale = ref<d3.ScaleLinear<number, number> | null>(null);
  const yScale = ref<d3.ScaleLinear<number, number> | null>(null);

  // 图层控制状态
  const layerControls = ref<LayerConfig>({
    showGrid: true,
    showPositiveDiagonal: false,
    showNegativeDiagonal: false,
    showCoordinateInfo: false,
    showTriangleCalculation: false,
    showLineDrawing: false,
    enableLineDrawing: false,
  });

  // 图层管理器注册表
  const layerManagers = new Map<string, LayerManager>();

  // 计算属性
  const plotWidth = computed(
    () => config.width - config.margin.left - config.margin.right
  );
  const plotHeight = computed(
    () => config.height - config.margin.top - config.margin.bottom
  );

  /**
   * 初始化SVG和坐标系
   */
  function initializeSVG() {
    if (isInitialized.value) return;

    // 清除现有内容
    d3.select(container).selectAll("*").remove();

    // 创建SVG
    const svgElement = d3
      .select(container)
      .append("svg")
      .attr("width", config.width)
      .attr("height", config.height);

    // 创建绘图区域
    const plotAreaElement = svgElement
      .append("g")
      .attr(
        "transform",
        `translate(${config.margin.left}, ${config.margin.top})`
      )
      .attr("preserveAspectRatio", "xMidYMid meet");

    // 创建比例尺
    const xScaleElement = d3
      .scaleLinear()
      .domain([0, 48])
      .range([0, plotWidth.value]);
    const yScaleElement = d3
      .scaleLinear()
      .domain([0, 48])
      .range([plotHeight.value, 0]);

    // 保存引用
    svg.value = svgElement;
    plotArea.value = plotAreaElement;
    xScale.value = xScaleElement;
    yScale.value = yScaleElement;

    isInitialized.value = true;
  }

  /**
   * 注册图层管理器
   */
  function registerLayer(name: string, manager: LayerManager) {
    layerManagers.set(name, manager);
  }

  /**
   * 注销图层管理器
   */
  function unregisterLayer(name: string) {
    const manager = layerManagers.get(name);
    if (manager) {
      manager.destroy();
      layerManagers.delete(name);
    }
  }

  /**
   * 切换图层显示状态
   */
  function toggleLayer(layerName: keyof LayerConfig) {
    const manager = layerManagers.get(layerName);
    if (manager) {
      manager.toggle(layerControls.value[layerName]);
    }
  }

  /**
   * 更新所有图层数据
   */
  function updateAllLayers(data: any) {
    layerManagers.forEach((manager) => {
      manager.update(data);
    });
  }

  /**
   * 渲染所有图层
   */
  function renderAllLayers() {
    layerManagers.forEach((manager) => {
      manager.render();
    });
  }

  /**
   * 销毁所有图层
   */
  function destroyAllLayers() {
    layerManagers.forEach((manager) => {
      manager.destroy();
    });
    layerManagers.clear();
  }

  /**
   * 获取当前的D3上下文
   */
  function getD3Context() {
    return {
      svg: svg.value,
      plotArea: plotArea.value,
      xScale: xScale.value,
      yScale: yScale.value,
      plotWidth: plotWidth.value,
      plotHeight: plotHeight.value,
    };
  }

  /**
   * 监听图层控制变化
   */
  watch(
    layerControls,
    (newControls, oldControls) => {
      if (!isInitialized.value) return;

      // 检查哪些图层状态发生了变化
      Object.keys(newControls).forEach((key) => {
        const layerKey = key as keyof LayerConfig;
        if (newControls[layerKey] !== oldControls?.[layerKey]) {
          toggleLayer(layerKey);
        }
      });
    },
    { deep: true }
  );

  /**
   * 组件卸载时清理
   */
  onUnmounted(() => {
    destroyAllLayers();
    if (svg.value) {
      svg.value.remove();
    }
  });

  // 初始化
  initializeSVG();

  return {
    // 状态
    isInitialized: computed(() => isInitialized.value),
    layerControls,

    // 计算属性
    plotWidth,
    plotHeight,

    // 方法
    getD3Context,
    registerLayer,
    unregisterLayer,
    toggleLayer,
    updateAllLayers,
    renderAllLayers,
    destroyAllLayers,

    // 事件
    events: events || {},
  };
}

// 导出类型已在上面定义
