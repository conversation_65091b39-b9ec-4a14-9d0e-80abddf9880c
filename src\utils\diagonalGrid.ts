interface DiagonalLine {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export class DiagonalGridCalculator {
  /**
   * 计算正对角线（左下到右上，斜率为1）
   * @param xScale D3的x轴比例尺
   * @param yScale D3的y轴比例尺
   * @param dataWidth 数据坐标系宽度（如48）
   * @param dataHeight 数据坐标系高度（如48）
   * @param spacing 数据坐标系中的间距
   */
  static calculatePositiveDiagonals(
    xScale: any,
    yScale: any,
    dataWidth: number,
    dataHeight: number,
    spacing: number
  ): DiagonalLine[] {
    const lines: DiagonalLine[] = [];

    // 计算需要的对角线数量
    // 对角线方程: y = x + c (在数据坐标系中)
    // c的范围: -dataWidth 到 dataHeight
    const minC = -dataWidth;
    const maxC = dataHeight;

    for (let c = minC; c <= maxC; c += spacing) {
      const line = this.createPositiveDiagonalLine(
        c,
        xScale,
        yScale,
        dataWidth,
        dataHeight
      );
      if (line) {
        lines.push(line);
      }
    }

    return lines;
  }

  /**
   * 计算负对角线（左上到右下，斜率为-1）
   * @param xScale D3的x轴比例尺
   * @param yScale D3的y轴比例尺
   * @param dataWidth 数据坐标系宽度（如48）
   * @param dataHeight 数据坐标系高度（如48）
   * @param spacing 数据坐标系中的间距
   */
  static calculateNegativeDiagonals(
    xScale: any,
    yScale: any,
    dataWidth: number,
    dataHeight: number,
    spacing: number
  ): DiagonalLine[] {
    const lines: DiagonalLine[] = [];

    // 计算需要的对角线数量
    // 对角线方程: y = -x + c (在数据坐标系中)
    // c的范围: 0 到 dataWidth + dataHeight
    const minC = 0;
    const maxC = dataWidth + dataHeight;

    for (let c = minC; c <= maxC; c += spacing) {
      const line = this.createNegativeDiagonalLine(
        c,
        xScale,
        yScale,
        dataWidth,
        dataHeight
      );
      if (line) {
        lines.push(line);
      }
    }

    return lines;
  }

  /**
   * 创建正对角线（y = x + c）
   */
  private static createPositiveDiagonalLine(
    c: number,
    xScale: any,
    yScale: any,
    dataWidth: number,
    dataHeight: number
  ): DiagonalLine | null {
    // 在数据坐标系中找到线段与边界的交点
    const intersections: Array<{ x: number; y: number }> = [];

    // 与左边界的交点 (x = 0)
    const yAtLeft = c;
    if (yAtLeft >= 0 && yAtLeft <= dataHeight) {
      intersections.push({ x: 0, y: yAtLeft });
    }

    // 与右边界的交点 (x = dataWidth)
    const yAtRight = dataWidth + c;
    if (yAtRight >= 0 && yAtRight <= dataHeight) {
      intersections.push({ x: dataWidth, y: yAtRight });
    }

    // 与底边界的交点 (y = 0)
    const xAtBottom = -c;
    if (xAtBottom >= 0 && xAtBottom <= dataWidth) {
      intersections.push({ x: xAtBottom, y: 0 });
    }

    // 与顶边界的交点 (y = dataHeight)
    const xAtTop = dataHeight - c;
    if (xAtTop >= 0 && xAtTop <= dataWidth) {
      intersections.push({ x: xAtTop, y: dataHeight });
    }

    // 去重并排序
    const uniqueIntersections = this.removeDuplicatePoints(intersections);

    if (uniqueIntersections.length >= 2) {
      // 转换为像素坐标
      return {
        x1: xScale(uniqueIntersections[0].x),
        y1: yScale(uniqueIntersections[0].y),
        x2: xScale(uniqueIntersections[1].x),
        y2: yScale(uniqueIntersections[1].y),
      };
    }

    return null;
  }

  /**
   * 创建负对角线（y = -x + c）
   */
  private static createNegativeDiagonalLine(
    c: number,
    xScale: any,
    yScale: any,
    dataWidth: number,
    dataHeight: number
  ): DiagonalLine | null {
    // 在数据坐标系中找到线段与边界的交点
    const intersections: Array<{ x: number; y: number }> = [];

    // 与左边界的交点 (x = 0)
    const yAtLeft = c;
    if (yAtLeft >= 0 && yAtLeft <= dataHeight) {
      intersections.push({ x: 0, y: yAtLeft });
    }

    // 与右边界的交点 (x = dataWidth)
    const yAtRight = c - dataWidth;
    if (yAtRight >= 0 && yAtRight <= dataHeight) {
      intersections.push({ x: dataWidth, y: yAtRight });
    }

    // 与底边界的交点 (y = 0)
    const xAtBottom = c;
    if (xAtBottom >= 0 && xAtBottom <= dataWidth) {
      intersections.push({ x: xAtBottom, y: 0 });
    }

    // 与顶边界的交点 (y = dataHeight)
    const xAtTop = c - dataHeight;
    if (xAtTop >= 0 && xAtTop <= dataWidth) {
      intersections.push({ x: xAtTop, y: dataHeight });
    }

    // 去重并排序
    const uniqueIntersections = this.removeDuplicatePoints(intersections);

    if (uniqueIntersections.length >= 2) {
      // 转换为像素坐标
      return {
        x1: xScale(uniqueIntersections[0].x),
        y1: yScale(uniqueIntersections[0].y),
        x2: xScale(uniqueIntersections[1].x),
        y2: yScale(uniqueIntersections[1].y),
      };
    }

    return null;
  }

  /**
   * 去除重复点并排序
   */
  private static removeDuplicatePoints(
    points: Array<{ x: number; y: number }>
  ): Array<{ x: number; y: number }> {
    const epsilon = 0.001; // 浮点数比较容差
    const unique: Array<{ x: number; y: number }> = [];

    for (const point of points) {
      const isDuplicate = unique.some(
        (existing) =>
          Math.abs(existing.x - point.x) < epsilon &&
          Math.abs(existing.y - point.y) < epsilon
      );

      if (!isDuplicate) {
        unique.push(point);
      }
    }

    // 按x坐标排序，如果x相同则按y排序
    return unique.sort((a, b) => {
      if (Math.abs(a.x - b.x) < epsilon) {
        return a.y - b.y;
      }
      return a.x - b.x;
    });
  }

  /**
   * 获取合适的对角线间距
   * 基于画布尺寸和现有网格密度计算
   */
  static getOptimalSpacing(
    width: number,
    height: number,
    gridSpacing: number
  ): number {
    // 对角线间距应该与网格间距保持一致，但可以适当调整密度
    // 由于对角线比水平/垂直线更密集，可以适当增加间距
    return gridSpacing * Math.sqrt(2); // 使用√2倍的间距，保持视觉平衡
  }
}

export type { DiagonalLine };
