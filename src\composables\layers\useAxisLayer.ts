import * as d3 from "d3";
import type { LayerManager } from "../useD3Visualization";

/**
 * 坐标轴图层管理器
 * 负责渲染X轴和Y轴的刻度和标签
 */
export class AxisLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private plotWidth: number;
  private plotHeight: number;
  private currentPer: number = 0;
  private isVisible: boolean = true;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    plotWidth: number,
    plotHeight: number
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.plotWidth = plotWidth;
    this.plotHeight = plotHeight;
  }

  /**
   * 更新当前期号
   */
  updateCurrentPer(per: number) {
    this.currentPer = per;
    // 当期号更新时，重新渲染坐标轴
    if (per > 0) {
      this.render();
    }
  }

  /**
   * 渲染坐标轴
   */
  render(): void {
    if (!this.isVisible) return;

    // 移除现有坐标轴
    this.plotArea.selectAll(".x-axis, .y-axis").remove();

    // 创建X轴（只有刻度和标签，没有网格线）
    const xAxis = d3
      .axisBottom(this.xScale)
      .ticks(48) // 恢复原有刻度数量
      .tickSize(6) // 正常的刻度线长度
      .tickFormat((d) => {
        return d.valueOf().toString().padStart(2, "0");
      })
      .tickSizeOuter(0);

    // 创建Y轴（只有刻度和标签，没有网格线）
    const yAxis = d3
      .axisLeft(this.yScale)
      .ticks(48) // 恢复原有刻度数量
      .tickSize(6) // 正常的刻度线长度
      .tickFormat((d) => {
        // Y轴期数显示逻辑：
        // yScale domain: [0, 48], range: [plotHeight, 0] (倒序)
        // d.valueOf() 范围是 0-48
        // 要求：从底部到顶部递减显示期数
        // 当 d = 0 时（底部），显示最新期号 currentPer
        // 当 d = 48 时（顶部），显示较早期号 currentPer-47
        const dValue = Number(d.valueOf());

        // 如果currentPer还没有加载，返回空字符串
        if (this.currentPer <= 0) {
          return "";
        }

        // 计算期数：从底部到顶部递减
        // 底部(d=0)显示 currentPer，顶部(d=48)显示 currentPer-47
        const periodNumber = this.currentPer - dValue + 11;

        // 只显示有效的期数（大于0）
        return periodNumber > 0 ? periodNumber.toString() : "";
      })
      .tickSizeOuter(0);

    // 渲染X轴
    const xAxisGroup = this.plotArea
      .append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0, ${this.plotHeight})`)
      .call(xAxis);

    // 设置X轴样式
    xAxisGroup
      .selectAll(".tick line")
      .style("stroke-width", 0.8)
      .style("stroke", "#666");

    // 隐藏X轴主线
    xAxisGroup.select(".domain").style("display", "none");

    // 渲染Y轴
    const yAxisGroup = this.plotArea
      .append("g")
      .attr("class", "y-axis")
      .call(yAxis);

    // 设置Y轴样式
    yAxisGroup
      .selectAll(".tick line")
      .style("stroke-width", 0.8)
      .style("stroke", "#666");

    // 隐藏Y轴主线
    yAxisGroup.select(".domain").style("display", "none");
  }

  /**
   * 更新数据
   */
  update(data: any): void {
    if (data && data.currentPer !== undefined) {
      this.updateCurrentPer(data.currentPer);
      this.render();
    }
  }

  /**
   * 切换显示状态
   */
  toggle(visible: boolean): void {
    this.isVisible = visible;
    if (visible) {
      this.render();
    } else {
      this.destroy();
    }
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.plotArea.selectAll(".x-axis, .y-axis").remove();
  }
}

/**
 * 坐标轴图层组合函数
 */
export function useAxisLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  plotWidth: number,
  plotHeight: number
) {
  const manager = new AxisLayerManager(
    plotArea,
    xScale,
    yScale,
    plotWidth,
    plotHeight
  );

  return {
    manager,
    updateCurrentPer: (per: number) => manager.updateCurrentPer(per),
  };
}
