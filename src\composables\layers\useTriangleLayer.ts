import * as d3 from 'd3';
import type { Coordinate } from '../useLottoData';
import type { LayerManager } from '../useD3Visualization';

/**
 * 三角形计算图层管理器
 * 负责处理ABC三角形的计算和连线显示
 */
export class TriangleLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private historyData: Coordinate[] = [];
  private isVisible: boolean = false;
  private currentTriangle: {
    pointA: Coordinate | null;
    pointB: Coordinate | null;
    pointC: Coordinate | null;
    isIsosceles: boolean;
  } = {
    pointA: null,
    pointB: null,
    pointC: null,
    isIsosceles: false,
  };
  private onTriangleUpdate?: (
    a: Coordinate,
    b: Coordinate,
    c: Coordinate,
    isIsosceles: boolean
  ) => void;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    options?: {
      onTriangleUpdate?: (a: Coordinate, b: Coordinate, c: Coordinate, isIsosceles: boolean) => void;
    }
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.onTriangleUpdate = options?.onTriangleUpdate;
  }

  /**
   * 更新历史数据
   */
  updateHistoryData(historyData: Coordinate[]) {
    this.historyData = historyData;
  }

  /**
   * 查找最近的两个点并判断是否形成等腰三角形
   */
  private findNearest(pointA: Coordinate): [Coordinate[], boolean] {
    // 计算每个点到点A的距离
    const distances = this.historyData.map((point, index) => {
      const dx = point.x - pointA.x;
      const dy = point.y - pointA.y;
      return {
        value: Math.sqrt(dx * dx + dy * dy),
        index: index,
      };
    });

    // 找到距离A点最近的两个点的索引
    const sortedDistances = [...distances];
    sortedDistances.sort((a, b) => a.value - b.value);

    const minIndices: number[] = [];
    minIndices.push(sortedDistances[1].index); // 从第二个开始，排除距离为0的自身

    if (sortedDistances[1].value === sortedDistances[2].value) {
      minIndices.push(sortedDistances[3].index);
    } else {
      minIndices.push(sortedDistances[2].index);
    }

    // 获取最近的两个点
    const closestPoints = minIndices.map((index) => this.historyData[index]);

    // 判断三点是否形成等腰三角形
    const pointB = closestPoints[0];
    const pointC = closestPoints[1];

    // 计算三边长度
    const distAB = Math.sqrt(
      Math.pow(pointA.x - pointB.x, 2) + Math.pow(pointA.y - pointB.y, 2)
    );
    const distBC = Math.sqrt(
      Math.pow(pointB.x - pointC.x, 2) + Math.pow(pointB.y - pointC.y, 2)
    );
    const distCA = Math.sqrt(
      Math.pow(pointC.x - pointA.x, 2) + Math.pow(pointC.y - pointA.y, 2)
    );

    // 判断是否为等腰三角形（两边相等）
    const epsilon = 0.001; // 误差容差
    const isIsosceles =
      Math.abs(distAB - distBC) < epsilon ||
      Math.abs(distBC - distCA) < epsilon ||
      Math.abs(distCA - distAB) < epsilon;

    return [closestPoints, isIsosceles];
  }

  /**
   * 计算并显示三角形
   */
  calculateTriangle(pointA: Coordinate) {
    if (!this.isVisible || this.historyData.length === 0) return;

    const [closestPoints, isIsosceles] = this.findNearest(pointA);
    const pointB = closestPoints[0];
    const pointC = closestPoints[1];

    // 更新当前三角形状态
    this.currentTriangle = {
      pointA,
      pointB,
      pointC,
      isIsosceles,
    };

    // 绘制三角形连线
    this.drawTriangleLines();

    // 触发回调
    this.onTriangleUpdate?.(pointA, pointB, pointC, isIsosceles);
  }

  /**
   * 绘制三角形连线
   */
  private drawTriangleLines() {
    const { pointA, pointB, pointC, isIsosceles } = this.currentTriangle;
    if (!pointA || !pointB || !pointC) return;

    // 清除之前的线
    this.plotArea.selectAll('#line-a, #line-b, #line-c').remove();

    const lineColor = isIsosceles ? 'green' : 'yellow';

    // 绘制A-B线
    this.plotArea
      .append('g')
      .lower()
      .append('line')
      .attr('id', 'line-a')
      .attr('x1', this.xScale(pointA.x))
      .attr('y1', this.yScale(pointA.y))
      .attr('x2', this.xScale(pointB.x))
      .attr('y2', this.yScale(pointB.y))
      .style('stroke', lineColor)
      .style('stroke-width', 2);

    // 绘制B-C线
    this.plotArea
      .append('g')
      .lower()
      .append('line')
      .attr('id', 'line-b')
      .attr('x1', this.xScale(pointB.x))
      .attr('y1', this.yScale(pointB.y))
      .attr('x2', this.xScale(pointC.x))
      .attr('y2', this.yScale(pointC.y))
      .style('stroke', lineColor)
      .style('stroke-width', 2);

    // 绘制C-A线
    this.plotArea
      .append('g')
      .lower()
      .append('line')
      .attr('id', 'line-c')
      .attr('x1', this.xScale(pointC.x))
      .attr('y1', this.yScale(pointC.y))
      .attr('x2', this.xScale(pointA.x))
      .attr('y2', this.yScale(pointA.y))
      .style('stroke', lineColor)
      .style('stroke-width', 2);
  }

  /**
   * 清除三角形连线
   */
  clearTriangleLines() {
    this.plotArea.selectAll('#line-a, #line-b, #line-c').remove();
    this.currentTriangle = {
      pointA: null,
      pointB: null,
      pointC: null,
      isIsosceles: false,
    };
  }

  /**
   * 获取当前三角形信息
   */
  getCurrentTriangle() {
    return { ...this.currentTriangle };
  }

  /**
   * 渲染图层
   */
  render(): void {
    // 三角形图层主要是响应式的，不需要主动渲染
    if (!this.isVisible) {
      this.clearTriangleLines();
    }
  }

  /**
   * 更新数据
   */
  update(data: any): void {
    if (data && data.historyData) {
      this.updateHistoryData(data.historyData);
    }
  }

  /**
   * 切换显示状态
   */
  toggle(visible: boolean): void {
    this.isVisible = visible;
    if (!visible) {
      this.clearTriangleLines();
    }
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.clearTriangleLines();
  }
}

/**
 * 三角形计算图层组合函数
 */
export function useTriangleLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  options?: {
    onTriangleUpdate?: (a: Coordinate, b: Coordinate, c: Coordinate, isIsosceles: boolean) => void;
  }
) {
  const manager = new TriangleLayerManager(plotArea, xScale, yScale, options);

  return {
    manager,
    calculateTriangle: (pointA: Coordinate) => manager.calculateTriangle(pointA),
    clearTriangleLines: () => manager.clearTriangleLines(),
    getCurrentTriangle: () => manager.getCurrentTriangle(),
    updateHistoryData: (historyData: Coordinate[]) => manager.updateHistoryData(historyData),
  };
}
