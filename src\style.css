:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  /* max-width: 1280px; */
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* 定义滚动条的宽度和颜色 */
::-webkit-scrollbar {
  width: 2px;
}

/* 定义滚动条的背景色 */
::-webkit-scrollbar-track {
  /* background-color: #333; */
  opacity: 0;
}

/* 定义滚动条thumb（滑块）的样式 */
::-webkit-scrollbar-thumb {
  background-color: #666;
  border-radius: 3px;
}

/* 定义滚动条hover状态下thumb（滑块）的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #888;
}

.g-hover:hover {
  cursor: pointer;
  /* 其他样式属性 */
}
