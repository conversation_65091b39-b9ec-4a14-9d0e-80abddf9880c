import { ref, computed } from "vue";
import { DataManager } from "../utils/dataManager";
import csv from "../components/lotto-20250406"; // 备用静态数据

export interface Coordinate {
  x: number;
  y: number;
  color: string;
  opacity: 0 | 1;
  display: "none" | "block";
  visibility: "hidden" | "visible";
  clicked: "clicked" | "";
}

// 全局状态
const rawCsvData = ref<string>("");
const isLoading = ref(false);
const error = ref<string | null>(null);
const lastUpdateTime = ref<Date | null>(null);
let per: number = 0;

/**
 * 彩票数据管理组合式函数
 */
export function useLottoData() {
  /**
   * 获取CSV数据 - 支持动态获取和静态备用
   */
  async function get_csv_data(): Promise<string[][]> {
    try {
      // 如果已有数据且不是静态数据，直接使用
      if (rawCsvData.value && rawCsvData.value !== csv) {
        return rawCsvData.value.split("\n").map((line) => line.split(","));
      }

      // 尝试从DataManager获取数据
      const data = await DataManager.getData();
      rawCsvData.value = data;
      return data.split("\n").map((line) => line.split(","));
    } catch (err) {
      console.warn("动态数据获取失败，使用静态备用数据:", err);
      // 使用静态备用数据
      rawCsvData.value = csv;
      return csv.split("\n").map((line) => line.split(","));
    }
  }

  /**
   * 强制更新数据
   */
  async function updateData(): Promise<void> {
    isLoading.value = true;
    error.value = null;

    try {
      const data = await DataManager.updateData();
      rawCsvData.value = data;
      lastUpdateTime.value = new Date();

      // 重新计算数据
      await refreshLottoData();

      console.log("数据更新成功");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "更新失败";
      error.value = errorMessage;
      console.error("数据更新失败:", err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 将CSV字符串转换为数字数组
   */
  async function convertCsvToResult(): Promise<number[][]> {
    const result: number[][] = [];

    const data = await get_csv_data();
    per = parseInt(data[0][0].replace(/"/g, ""), 10);

    for (let line of data.slice(0, 38)) {
      const row: number[] = [];
      for (let i = 0; i < line.length; i++) {
        row.push(parseInt(line[i].replace(/"/g, ""), 10));
      }
      // 将最后两列的数加上35（特殊号码处理）
      row[row.length - 2] += 35;
      row[row.length - 1] += 35;
      result.push(row);
    }

    console.log("期号:", per);
    console.log("转换后的数据:", result);
    return result;
  }

  /**
   * 计算坐标和颜色
   */
  function calculateCoordinatesAndColors(
    list1: number[][]
  ): [Coordinate[], Coordinate[]] {
    const colors = ["#adb5bd", "#f06595", "#51cf66", "#ffe066", "#f76707"];
    let list2: Coordinate[] = [];

    // 计算每个数的坐标和颜色
    for (let i = 0; i < list1.length; i++) {
      const row = list1[i];

      for (let j = 1; j < row.length; j++) {
        const x = row[j];
        const y = i + 11;
        const colorIndex = x % 5;
        const color = colors[colorIndex > 0 ? colorIndex - 1 : 4];

        list2.push({
          x,
          y,
          color,
          opacity: 1,
          display: "block",
          visibility: "visible",
          clicked: "clicked",
        });
      }
    }

    const list3 = list2.concat(); // 复制一份

    // 计算y轴为1到10的坐标和颜色（选择区域）
    for (let y = 1; y <= 10; y++) {
      for (let x = 1; x <= 47; x++) {
        const colorIndex = x % 5;
        const color = colors[colorIndex > 0 ? colorIndex - 1 : 4];

        list3.push({
          x,
          y,
          color,
          opacity: 0,
          display: "none",
          visibility: "hidden",
          clicked: "",
        });
      }
    }

    return [list3, list2];
  }

  /**
   * 查找最近的两个点并判断是否形成等腰三角形
   */
  function findNearest(
    points: Coordinate[],
    pointA: Coordinate
  ): [Coordinate[], boolean] {
    // 计算每个点到点A的距离
    const distances = points.map(function (point, index) {
      const dx = point.x - pointA.x;
      const dy = point.y - pointA.y;
      return {
        value: Math.sqrt(dx * dx + dy * dy),
        index: index,
      };
    });

    // 找到距离A点最近的两个点的索引
    const sortedDistances = [...distances];
    sortedDistances.sort((a, b) => a.value - b.value);

    const minIndices: number[] = [];
    minIndices.push(sortedDistances[1].index); // 从第二个开始，排除距离为0的自身

    if (sortedDistances[1].value === sortedDistances[2].value) {
      minIndices.push(sortedDistances[3].index);
    } else {
      minIndices.push(sortedDistances[2].index);
    }

    // 获取最近的两个点
    const closestPoints = minIndices.map(function (index) {
      return points[index];
    });

    // 判断三点是否形成等腰三角形
    const pointB = closestPoints[0];
    const pointC = closestPoints[1];

    // 计算三边长度
    const distAB = Math.sqrt(
      Math.pow(pointA.x - pointB.x, 2) + Math.pow(pointA.y - pointB.y, 2)
    );
    const distBC = Math.sqrt(
      Math.pow(pointB.x - pointC.x, 2) + Math.pow(pointB.y - pointC.y, 2)
    );
    const distCA = Math.sqrt(
      Math.pow(pointC.x - pointA.x, 2) + Math.pow(pointC.y - pointA.y, 2)
    );

    // 判断是否为等腰三角形（两边相等）
    const epsilon = 0.001; // 误差容差
    const isIsosceles =
      Math.abs(distAB - distBC) < epsilon ||
      Math.abs(distBC - distCA) < epsilon ||
      Math.abs(distCA - distAB) < epsilon;

    return [closestPoints, isIsosceles];
  }

  /**
   * 查找纵向最近的点
   */
  function findClosestPoint(
    coordinates: Coordinate[],
    pointA: Coordinate
  ): Coordinate {
    let minDistance = Infinity;
    let closestPoint: Coordinate = {
      x: 0,
      y: 0,
      color: "",
      clicked: "",
      opacity: 0,
      display: "none",
      visibility: "hidden",
    };

    coordinates.forEach((pointB) => {
      if (pointB.y > pointA.y) {
        const distance = Math.abs(pointB.y - pointA.y);
        const horizontalDistance = Math.abs(pointB.x - pointA.x);

        if (distance < minDistance && horizontalDistance === distance) {
          minDistance = distance;
          closestPoint = pointB;
        }
      }
    });

    return closestPoint;
  }

  // 响应式数据
  const lottoDataRef = ref<[Coordinate[], Coordinate[]] | null>(null);

  /**
   * 刷新彩票数据
   */
  async function refreshLottoData() {
    try {
      const result = await convertCsvToResult();
      const data = calculateCoordinatesAndColors(result);
      lottoDataRef.value = data;
    } catch (err) {
      console.error("刷新数据失败:", err);
      throw err;
    }
  }

  // 计算属性
  const lottoData = computed(() => lottoDataRef.value?.[0] || []);
  const lottoData2 = computed(() => lottoDataRef.value?.[1] || []);
  const currentPer = computed(() => per);

  // 初始化数据
  const initializeData = async () => {
    try {
      await refreshLottoData();
    } catch (err) {
      console.error("初始化数据失败:", err);
    }
  };

  return {
    // 数据
    lottoData,
    lottoData2,
    currentPer,

    // 状态
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    lastUpdateTime: computed(() => lastUpdateTime.value),

    // 方法
    updateData,
    refreshLottoData,
    initializeData,
    findNearest,
    findClosestPoint,
    get_csv_data,

    // 类型
    type: {} as { Coordinate: Coordinate },
  };
}
