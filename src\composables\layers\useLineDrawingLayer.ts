import * as d3 from "d3";
import type { Coordinate } from "../useLottoData";
import type { LayerManager } from "../useD3Visualization";

// 连线点接口
interface LinePoint {
  x: number;
  y: number;
  timestamp: number;
  id: string;
}

// 连线接口
interface DrawnLine {
  id: string;
  startPoint: LinePoint;
  endPoint: LinePoint;
  style: {
    color: string;
    width: number;
    dashArray?: string;
  };
}

// 连线绘制状态
interface LineDrawingState {
  isEnabled: boolean;
  currentPoints: LinePoint[];
  drawnLines: DrawnLine[];
  nextPointId: number;
}

// 本地存储键名
const STORAGE_KEY = "lotto_drawn_lines";

/**
 * 连线绘制图层管理器
 * 负责用户自定义连线的绘制和管理
 */
export class LineDrawingLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private state: LineDrawingState;
  private onPointClick?: (point: Coordinate) => void;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    options?: {
      onPointClick?: (point: Coordinate) => void;
    }
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.onPointClick = options?.onPointClick;

    // 初始化状态
    this.state = {
      isEnabled: false,
      currentPoints: [],
      drawnLines: [],
      nextPointId: 1,
    };

    // 从本地存储加载数据
    this.loadFromStorage();
  }

  /**
   * 启用/禁用连线绘制功能
   */
  setEnabled(enabled: boolean) {
    this.state.isEnabled = enabled;

    // 设置鼠标样式
    if (enabled) {
      this.plotArea.style("cursor", "crosshair");
      this.render();
    } else {
      this.plotArea.style("cursor", "default");
      this.render();
    }
  }

  /**
   * 检查两点之间是否已存在连线
   */
  private lineExists(point1: LinePoint, point2: LinePoint): boolean {
    return this.state.drawnLines.some(
      (line) =>
        (line.startPoint.x === point1.x &&
          line.startPoint.y === point1.y &&
          line.endPoint.x === point2.x &&
          line.endPoint.y === point2.y) ||
        (line.startPoint.x === point2.x &&
          line.startPoint.y === point2.y &&
          line.endPoint.x === point1.x &&
          line.endPoint.y === point1.y)
    );
  }

  /**
   * 处理点击事件
   */
  handlePointClick(coordinate: Coordinate) {
    if (!this.state.isEnabled) return;

    const newPoint: LinePoint = {
      x: coordinate.x,
      y: coordinate.y,
      timestamp: Date.now(),
      id: `point_${this.state.nextPointId++}`,
    };

    this.state.currentPoints.push(newPoint);

    // 如果有两个或更多点，创建连线
    if (this.state.currentPoints.length >= 2) {
      const startPoint =
        this.state.currentPoints[this.state.currentPoints.length - 2];
      const endPoint =
        this.state.currentPoints[this.state.currentPoints.length - 1];

      // 检查是否已存在相同的连线
      if (this.lineExists(startPoint, endPoint)) {
        console.log("连线已存在，跳过重复绘制");
        return;
      }

      const newLine: DrawnLine = {
        id: `line_${startPoint.id}_${endPoint.id}`,
        startPoint,
        endPoint,
        style: {
          color: "#ff6b6b",
          width: 2,
          dashArray: "5,5",
        },
      };

      this.state.drawnLines.push(newLine);
    }

    // 保存到本地存储
    this.saveToStorage();

    // 重新渲染
    this.render();

    // 触发回调
    this.onPointClick?.(coordinate);
  }

  /**
   * 清除所有连线
   */
  clearAllLines() {
    this.state.currentPoints = [];
    this.state.drawnLines = [];
    this.state.nextPointId = 1;
    this.saveToStorage();
    this.render();
  }

  /**
   * 撤销最后一条连线
   */
  undoLastLine() {
    if (this.state.drawnLines.length > 0) {
      this.state.drawnLines.pop();
      // 也移除对应的点
      if (this.state.currentPoints.length > 0) {
        this.state.currentPoints.pop();
      }
      this.saveToStorage();
      this.render();
    }
  }

  /**
   * 渲染连线
   */
  render(): void {
    // 移除现有连线
    this.plotArea.selectAll(".user-drawn-lines").remove();

    if (this.state.drawnLines.length === 0) return;

    // 创建连线组
    const linesGroup = this.plotArea
      .append("g")
      .attr("class", "user-drawn-lines")
      .lower(); // 确保连线在数据点下方

    // 绘制所有连线
    linesGroup
      .selectAll("line")
      .data(this.state.drawnLines)
      .enter()
      .append("line")
      .attr("x1", (d) => this.xScale(d.startPoint.x))
      .attr("y1", (d) => this.yScale(d.startPoint.y))
      .attr("x2", (d) => this.xScale(d.endPoint.x))
      .attr("y2", (d) => this.yScale(d.endPoint.y))
      .style("stroke", (d) => d.style.color)
      .style("stroke-width", (d) => d.style.width)
      .style("stroke-dasharray", (d) => d.style.dashArray || "none")
      .style("opacity", 0.8);

    // 绘制连接点（可选，用于视觉反馈）
    if (this.state.isEnabled && this.state.currentPoints.length > 0) {
      linesGroup
        .selectAll("circle.connection-point")
        .data(this.state.currentPoints)
        .enter()
        .append("circle")
        .attr("class", "connection-point")
        .attr("cx", (d) => this.xScale(d.x))
        .attr("cy", (d) => this.yScale(d.y))
        .attr("r", 3)
        .style("fill", "#ff6b6b")
        .style("stroke", "#fff")
        .style("stroke-width", 1);
    }
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage() {
    try {
      const data = {
        version: "1.0",
        timestamp: Date.now(),
        state: this.state,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn("Failed to save line drawing data to localStorage:", error);
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromStorage() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        if (data.version === "1.0" && data.state) {
          this.state = { ...this.state, ...data.state };
          // 确保启用状态为false（需要用户手动启用）
          this.state.isEnabled = false;
        }
      }
    } catch (error) {
      console.warn(
        "Failed to load line drawing data from localStorage:",
        error
      );
    }
  }

  /**
   * 获取当前状态
   */
  getState(): LineDrawingState {
    return { ...this.state };
  }

  /**
   * 更新数据
   */
  update(_data: any): void {
    // 连线绘制不依赖外部数据，只需重新渲染
    this.render();
  }

  /**
   * 切换显示状态
   */
  toggle(visible: boolean): void {
    if (visible) {
      this.render();
    } else {
      this.plotArea.selectAll(".user-drawn-lines").remove();
    }
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.plotArea.selectAll(".user-drawn-lines").remove();
  }
}

/**
 * 连线绘制图层组合函数
 */
export function useLineDrawingLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  options?: {
    onPointClick?: (point: Coordinate) => void;
  }
) {
  const manager = new LineDrawingLayerManager(
    plotArea,
    xScale,
    yScale,
    options
  );

  return {
    manager,
    setEnabled: (enabled: boolean) => manager.setEnabled(enabled),
    handlePointClick: (coordinate: Coordinate) =>
      manager.handlePointClick(coordinate),
    clearAllLines: () => manager.clearAllLines(),
    undoLastLine: () => manager.undoLastLine(),
    getState: () => manager.getState(),
  };
}
