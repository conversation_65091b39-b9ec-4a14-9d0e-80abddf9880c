import * as d3 from "d3";
import type { Coordinate } from "../useLottoData";
import type { LayerManager } from "../useD3Visualization";

/**
 * 数据点图层管理器
 * 负责渲染数据点（圆点和文本标签）
 */
export class DataPointsLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private data: Coordinate[] = [];
  private historyData: Coordinate[] = [];
  private isVisible: boolean = true;
  private onPointClick?: (point: Coordinate) => void;
  private onPointHover?: (point: Coordinate | null) => void;
  private isLineDrawingMode: boolean = false;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    options?: {
      onPointClick?: (point: Coordinate) => void;
      onPointHover?: (point: Coordinate | null) => void;
    }
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.onPointClick = options?.onPointClick;
    this.onPointHover = options?.onPointHover;
  }

  /**
   * 更新数据
   */
  updateData(allData: Coordinate[], historyData: Coordinate[]) {
    this.data = allData;
    this.historyData = historyData;
  }

  /**
   * 设置连线绘制模式
   */
  setLineDrawingMode(enabled: boolean) {
    this.isLineDrawingMode = enabled;
  }

  /**
   * 渲染数据点
   */
  render(): void {
    if (!this.isVisible || this.data.length === 0) return;

    // 移除现有数据点
    this.plotArea.selectAll(".data-points-group").remove();

    // 创建数据点组
    const pointsGroup = this.plotArea
      .append("g")
      .attr("class", "data-points-group");

    // 创建坐标点
    pointsGroup
      .selectAll("circle")
      .data(this.data)
      .enter()
      .append("circle")
      .attr("cx", (d) => this.xScale(d.x))
      .attr("cy", (d) => this.yScale(d.y))
      .attr("r", 10)
      .attr("fill", (d) => d.color)
      .attr("class", (d) => `${d.clicked} point`)
      .style("opacity", (d) => d.opacity)
      .on("mouseover", (event, d) => {
        this.handleMouseOver(event.target, d);
      })
      .on("mouseout", (event) => {
        this.handleMouseOut(event.target);
      })
      .on("click", (event, d) => {
        this.handleClick(event.target, d);
      })
      .raise();

    // 添加文本标签显示历史数据点的数值
    pointsGroup
      .selectAll("text")
      .data(this.historyData)
      .enter()
      .append("text")
      .text((d) => d.x.toString().padStart(2, "0"))
      .attr("x", (d) => this.xScale(d.x))
      .attr("y", (d) => this.yScale(d.y))
      .attr("text-anchor", "middle")
      .attr("alignment-baseline", "middle")
      .style("font-size", "10px")
      .style("fill", "black")
      .style("pointer-events", "none");
  }

  /**
   * 处理鼠标悬停
   */
  private handleMouseOver(element: any, d: Coordinate) {
    const selection = d3.select(element);
    const isClicked = selection.classed("clicked");

    // hover时显示圆点（如果不是已点击状态）
    if (!isClicked) {
      selection.style("opacity", "1");
    }

    // hover时显示临时数字（如果没有持久数字）
    const existingText = this.plotArea.select(`.text-for-${d.x}-${d.y}`);
    if (existingText.empty()) {
      this.plotArea
        .append("text")
        .attr("class", `temp-text-for-${d.x}-${d.y}`)
        .text(d.x.toString().padStart(2, "0"))
        .attr("x", this.xScale(d.x))
        .attr("y", this.yScale(d.y))
        .attr("text-anchor", "middle")
        .attr("alignment-baseline", "middle")
        .style("font-size", "10px")
        .style("fill", "black")
        .style("pointer-events", "none")
        .style("opacity", 0.7); // 临时文字稍微透明
    }

    this.onPointHover?.(d);
  }

  /**
   * 处理鼠标离开
   */
  private handleMouseOut(element: any) {
    const selection = d3.select(element);
    const originalOpacity = selection.datum() as Coordinate;
    const isClicked = selection.classed("clicked");

    // 只有未点击的点才恢复原始透明度
    if (!isClicked) {
      selection.style("opacity", originalOpacity.opacity);
    }

    // 移除临时数字（保留持久数字）
    const d = originalOpacity;
    this.plotArea.select(`.temp-text-for-${d.x}-${d.y}`).remove();

    this.onPointHover?.(null);
  }

  /**
   * 处理点击事件
   */
  private handleClick(element: any, d: Coordinate) {
    const selection = d3.select(element);
    const isClicked = !selection.classed("clicked");
    selection.classed("clicked", isClicked);

    if (isClicked) {
      // 点击后持久显示圆点和数字

      // 移除临时文字（如果存在）
      this.plotArea.select(`.temp-text-for-${d.x}-${d.y}`).remove();

      // 显示圆点（包括原本隐藏的选择区域点）
      selection.style("opacity", 1).attr("fill", d.color).attr("r", 10); // 确保半径一致

      // 显示持久数字
      const existingText = this.plotArea.select(`.text-for-${d.x}-${d.y}`);
      if (existingText.empty()) {
        this.plotArea
          .append("text")
          .attr("class", `text-for-${d.x}-${d.y}`)
          .text(d.x.toString().padStart(2, "0"))
          .attr("x", this.xScale(d.x))
          .attr("y", this.yScale(d.y))
          .attr("text-anchor", "middle")
          .attr("alignment-baseline", "middle")
          .style("font-size", "10px")
          .style("fill", "black")
          .style("pointer-events", "none")
          .style("opacity", 1); // 持久文字完全不透明
      }
    } else {
      // 取消点击状态

      // 移除持久数字
      this.plotArea.select(`.text-for-${d.x}-${d.y}`).remove();

      // 恢复圆点原始状态
      if (d.opacity === 0) {
        // 选择区域的点恢复隐藏
        selection.style("opacity", 0);
      } else {
        // 历史数据点保持可见但恢复原始透明度
        selection.style("opacity", d.opacity);
      }
    }

    this.onPointClick?.(d);
  }

  /**
   * 高亮显示指定点
   */
  highlightPoint(point: Coordinate | null, highlight: boolean = true) {
    if (!point) {
      // 清除所有高亮
      this.plotArea.selectAll(".point").style("stroke", "none").attr("r", 10);
      return;
    }

    const pointElement = this.plotArea
      .selectAll(".point")
      .filter((d: any) => d.x === point.x && d.y === point.y);

    if (highlight) {
      // 根据是否为连线绘制模式使用不同的高亮样式
      const strokeColor = this.isLineDrawingMode ? "#ff6b6b" : "#fff";
      const strokeWidth = this.isLineDrawingMode ? 3 : 2;

      pointElement
        .transition()
        .duration(150)
        .attr("r", 12)
        .style("stroke", strokeColor)
        .style("stroke-width", strokeWidth);
    } else {
      pointElement
        .transition()
        .duration(150)
        .attr("r", 10)
        .style("stroke", "none");
    }
  }

  /**
   * 更新数据
   */
  update(data: any): void {
    if (data && data.allData && data.historyData) {
      this.updateData(data.allData, data.historyData);
      this.render();
    }
  }

  /**
   * 切换显示状态
   */
  toggle(visible: boolean): void {
    this.isVisible = visible;
    if (visible) {
      this.render();
    } else {
      this.destroy();
    }
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.plotArea.selectAll(".data-points-group").remove();
    // 清除所有动态添加的文本标签（包括持久和临时）
    this.plotArea.selectAll('[class^="text-for-"]').remove();
    this.plotArea.selectAll('[class^="temp-text-for-"]').remove();
  }
}

/**
 * 数据点图层组合函数
 */
export function useDataPointsLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  options?: {
    onPointClick?: (point: Coordinate) => void;
    onPointHover?: (point: Coordinate | null) => void;
  }
) {
  const manager = new DataPointsLayerManager(plotArea, xScale, yScale, options);

  return {
    manager,
    updateData: (allData: Coordinate[], historyData: Coordinate[]) =>
      manager.updateData(allData, historyData),
    highlightPoint: (point: Coordinate | null, highlight?: boolean) =>
      manager.highlightPoint(point, highlight),
    setLineDrawingMode: (enabled: boolean) =>
      manager.setLineDrawingMode(enabled),
  };
}
