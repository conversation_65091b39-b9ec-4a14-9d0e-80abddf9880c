<template>
  <h3>Lotto D3</h3>
  <div id="chart" ref="chart"></div>
  <br />

  <!-- 坐标信息显示 -->
  <div
    v-if="currentHoverPoint && visualization?.layerControls.showCoordinateInfo"
    class="coordinate-info"
  >
    <span
      >当前坐标: ({{ currentHoverPoint.x }}, {{ currentHoverPoint.y }})</span
    >
  </div>

  <!-- 三角形计算信息显示 -->
  <div
    v-if="
      visualization?.layerControls.showTriangleCalculation &&
      (axisA || axisB || axisC)
    "
    class="triangle-info"
  >
    <span v-if="axisA && axisB && axisC">
      A(x:{{ axisA.x }}, y:{{ axisA.y }}) B(x:{{ axisB.x }}, y:{{ axisB.y }})
      C(x:{{ axisC.x }}, y:{{ axisC.y }})
      <span class="triangle-type" :class="{ isosceles: isIsoscelesTriangle }">
        {{ isIsoscelesTriangle ? "[等腰三角形]" : "[普通三角形]" }}
      </span>
    </span>
  </div>

  <br />

  <!-- 控制面板 -->
  <div class="controls">
    <button @click="toggle">全屏</button>
    <button @click="handleUpdateData" :disabled="isLoading" class="update-btn">
      {{ isLoading ? "更新中..." : "更新数据" }}
    </button>
  </div>

  <!-- 图层控制面板 -->
  <div class="layer-controls">
    <h4>图层控制</h4>
    <div class="checkbox-group">
      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showGrid"
          @change="toggleLayer('showGrid')"
        />
        <span>显示网格线</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="layerControls.showPositiveDiagonal"
          @change="toggleLayer('positiveDiagonal')"
        />
        <span>显示正对角线</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="layerControls.showNegativeDiagonal"
          @change="toggleLayer('negativeDiagonal')"
        />
        <span>显示负对角线</span>
      </label>

      <label class="checkbox-item">
        <input type="checkbox" v-model="layerControls.showCoordinateInfo" />
        <span>显示坐标信息</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="layerControls.showTriangleCalculation"
        />
        <span>显示三角形计算</span>
      </label>
    </div>
  </div>

  <!-- 状态提示 -->
  <div v-if="error" class="error-message">❌ {{ error }}</div>
  <div v-if="lastUpdateTime" class="update-info">
    📅 最后更新: {{ formatUpdateTime(lastUpdateTime) }}
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { useFullscreen } from "@vueuse/core";
import { useLottoData } from "../composables/useLottoData";
import { useD3Visualization } from "../composables/useD3Visualization";
import { useAxisLayer } from "../composables/layers/useAxisLayer";
import { useGridLayer } from "../composables/layers/useGridLayer";
import { useDiagonalLayer } from "../composables/layers/useDiagonalLayer";
import { useDataPointsLayer } from "../composables/layers/useDataPointsLayer";
import { useTriangleLayer } from "../composables/layers/useTriangleLayer";

import type { Coordinate } from "../composables/useLottoData";

// 使用彩票数据组合式函数
const {
  lottoData,
  lottoData2,
  currentPer,
  isLoading,
  error,
  lastUpdateTime,
  updateData,
  initializeData,
} = useLottoData();

// 容器引用
const chart = ref<HTMLElement | null>(null);

// 三角形状态
const axisA = ref<Coordinate>();
const axisB = ref<Coordinate>();
const axisC = ref<Coordinate>();
const isIsoscelesTriangle = ref(false);

// 当前悬停点信息
const currentHoverPoint = ref<Coordinate | null>(null);

const { toggle } = useFullscreen(chart as any);

// 可视化配置
const visualizationConfig = {
  width: 1300,
  height: 1300,
  margin: { top: 20, right: 20, bottom: 35, left: 35 },
};

// D3可视化组合函数（延迟初始化）
let visualization: ReturnType<typeof useD3Visualization> | null = null;
let axisLayer: ReturnType<typeof useAxisLayer> | null = null;
let gridLayer: ReturnType<typeof useGridLayer> | null = null;
let diagonalLayer: ReturnType<typeof useDiagonalLayer> | null = null;
let dataPointsLayer: ReturnType<typeof useDataPointsLayer> | null = null;
let triangleLayer: ReturnType<typeof useTriangleLayer> | null = null;

/**
 * 初始化可视化系统
 */
function initializeVisualization() {
  if (!chart.value || visualization) return;

  // 初始化核心可视化系统
  visualization = useD3Visualization(chart.value, visualizationConfig, {
    onPointHover: (point) => {
      currentHoverPoint.value = point;
      if (point && dataPointsLayer) {
        dataPointsLayer.highlightPoint(point, true);
      } else if (dataPointsLayer) {
        dataPointsLayer.highlightPoint(null, false);
      }
    },
    onPointClick: (point) => {
      // 处理点击事件
      console.log("Point clicked:", point);
    },
    onTriangleUpdate: (a, b, c, isIsosceles) => {
      axisA.value = a;
      axisB.value = b;
      axisC.value = c;
      isIsoscelesTriangle.value = isIsosceles;
    },
  });

  // 获取D3上下文
  const context = visualization.getD3Context();
  if (!context.plotArea || !context.xScale || !context.yScale) return;

  // 初始化各个图层
  axisLayer = useAxisLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  gridLayer = useGridLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  diagonalLayer = useDiagonalLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  dataPointsLayer = useDataPointsLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    {
      onPointClick: (point) => {
        if (
          visualization?.layerControls.showTriangleCalculation &&
          triangleLayer
        ) {
          triangleLayer.calculateTriangle(point);
        }
      },
      onPointHover: (point) => {
        currentHoverPoint.value = point;
      },
    }
  );

  triangleLayer = useTriangleLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    {
      onTriangleUpdate: (a, b, c, isIsosceles) => {
        axisA.value = a;
        axisB.value = b;
        axisC.value = c;
        isIsoscelesTriangle.value = isIsosceles;
      },
    }
  );

  // 注册图层到可视化系统
  visualization.registerLayer("axis", axisLayer.manager);
  visualization.registerLayer("showGrid", gridLayer.manager);
  visualization.registerLayer("showPositiveDiagonal", diagonalLayer.manager);
  visualization.registerLayer("showNegativeDiagonal", diagonalLayer.manager);
  visualization.registerLayer("showCoordinateInfo", dataPointsLayer.manager);
  visualization.registerLayer("showTriangleCalculation", triangleLayer.manager);

  // 初始渲染
  renderAllLayers();
}

/**
 * 更新所有图层数据
 */
function updateAllLayersData() {
  if (!visualization || !axisLayer || !dataPointsLayer || !triangleLayer)
    return;

  const data = {
    currentPer: currentPer.value,
    allData: lottoData.value,
    historyData: lottoData2.value,
  };

  // 更新坐标轴期号
  axisLayer.updateCurrentPer(currentPer.value);

  // 更新数据点
  dataPointsLayer.updateData(lottoData.value, lottoData2.value);

  // 更新三角形计算的历史数据
  triangleLayer.updateHistoryData(lottoData2.value);

  // 更新所有图层
  visualization.updateAllLayers(data);
}

/**
 * 渲染所有图层
 */
function renderAllLayers() {
  if (!visualization) return;
  visualization.renderAllLayers();
}

/**
 * 图层控制函数
 */
function toggleLayer(
  layerType: keyof typeof visualization.layerControls.value
) {
  if (!visualization) return;

  switch (layerType) {
    case "showGrid":
      visualization.toggleLayer("showGrid");
      break;
    case "showPositiveDiagonal":
      if (diagonalLayer) {
        diagonalLayer.togglePositive(
          visualization.layerControls.showPositiveDiagonal
        );
      }
      break;
    case "showNegativeDiagonal":
      if (diagonalLayer) {
        diagonalLayer.toggleNegative(
          visualization.layerControls.showNegativeDiagonal
        );
      }
      break;
    case "showCoordinateInfo":
      visualization.toggleLayer("showCoordinateInfo");
      break;
    case "showTriangleCalculation":
      visualization.toggleLayer("showTriangleCalculation");
      break;
  }
}

// 清理旧的全局变量，现在使用组合函数管理

onMounted(async () => {
  // 初始化数据
  await initializeData();

  // 初始化可视化系统
  initializeVisualization();

  // 更新所有图层数据
  updateAllLayersData();
});

// 数据更新处理函数
async function handleUpdateData() {
  await updateData();
  updateAllLayersData();
}

// 监听数据变化，自动更新图层
watch([lottoData, lottoData2, currentPer], () => {
  updateAllLayersData();
});

</script>
  const xAxis = d3
    .axisBottom(svgXScale)
    .ticks(48)
    .tickSize(6) // 正常的刻度线长度
    .tickFormat(function (d) {
      return d.valueOf().toString().padStart(2, "0");
    })
    .tickSizeOuter(0);

  // 创建Y轴（只有刻度和标签，没有网格线）
  const yAxis = d3
    .axisLeft(svgYScale)
    .ticks(48)
    .tickSize(6) // 正常的刻度线长度
    .tickFormat(function (d) {
      const v = currentPer.value + 11 - Number(d.valueOf());
      return v.toString();
    })
    .tickSizeOuter(0);

  // 渲染X轴
  const xAxisGroup = svgPlotArea
    .append("g")
    .attr("class", "x-axis")
    .attr("transform", `translate(0, ${plotHeight})`)
    .call(xAxis);

  // 设置X轴样式
  xAxisGroup
    .selectAll(".tick line")
    .style("stroke-width", 0.5)
    .style("stroke", "#666");

  // 隐藏X轴主线
  xAxisGroup.select(".domain").style("display", "none");

  // 渲染Y轴
  const yAxisGroup = svgPlotArea
    .append("g")
    .attr("class", "y-axis")
    .call(yAxis);

  // 设置Y轴样式
  yAxisGroup
    .selectAll(".tick line")
    .style("stroke-width", 0.5)
    .style("stroke", "#666");

  // 隐藏Y轴主线
  yAxisGroup.select(".domain").style("display", "none");
}

// 渲染网格线（可选显示）
function renderGrid() {
  if (!svgPlotArea || !svgXScale || !svgYScale) return;

  // 移除现有网格线
  svgPlotArea.selectAll(".x-grid, .y-grid").remove();

  // 创建X轴网格线（延长的tick线）
  const xGrid = d3
    .axisBottom(svgXScale)
    .ticks(48)
    .tickSize(-plotHeight) // 延长为网格线
    .tickFormat(() => "") // 不显示标签
    .tickSizeOuter(0);

  // 创建Y轴网格线（延长的tick线）
  const yGrid = d3
    .axisLeft(svgYScale)
    .ticks(48)
    .tickSize(-plotWidth) // 延长为网格线
    .tickFormat(() => "") // 不显示标签
    .tickSizeOuter(0);

  // 渲染X轴网格线
  const xGridGroup = svgPlotArea
    .append("g")
    .attr("class", "x-grid")
    .attr("transform", `translate(0, ${plotHeight})`)
    .call(xGrid);

  // 设置网格线样式
  xGridGroup
    .selectAll(".tick line")
    .style("stroke-width", 0.1)
    .style("stroke", "#ccc")
    .style("opacity", 0.3);

  // 隐藏坐标轴主线
  xGridGroup.select(".domain").style("display", "none");

  // 渲染Y轴网格线
  const yGridGroup = svgPlotArea
    .append("g")
    .attr("class", "y-grid")
    .call(yGrid);

  // 设置网格线样式
  yGridGroup
    .selectAll(".tick line")
    .style("stroke-width", 0.1)
    .style("stroke", "#ccc")
    .style("opacity", 0.3);

  // 隐藏坐标轴主线
  yGridGroup.select(".domain").style("display", "none");
}

// 渲染正对角线
function renderPositiveDiagonal() {
  if (!svgPlotArea || !svgXScale || !svgYScale) return;

  // 移除现有正对角线
  svgPlotArea.selectAll(".diagonal-grid-positive").remove();

  const dataSpacing = 2;
  const dataWidth = 48;
  const dataHeight = 48;

  const positiveDiagonals = DiagonalGridCalculator.calculatePositiveDiagonals(
    svgXScale,
    svgYScale,
    dataWidth,
    dataHeight,
    dataSpacing
  );

  svgPlotArea
    .append("g")
    .attr("class", "diagonal-grid-positive")
    .lower()
    .selectAll("line")
    .data(positiveDiagonals)
    .enter()
    .append("line")
    .attr("x1", (d: any) => d.x1)
    .attr("y1", (d: any) => d.y1)
    .attr("x2", (d: any) => d.x2)
    .attr("y2", (d: any) => d.y2)
    .style("stroke", "#888")
    .style("stroke-width", 0.8)
    .style("stroke-opacity", 0.6)
    .style("stroke-dasharray", "2,2");
}

// 渲染负对角线
function renderNegativeDiagonal() {
  if (!svgPlotArea || !svgXScale || !svgYScale) return;

  // 移除现有负对角线
  svgPlotArea.selectAll(".diagonal-grid-negative").remove();

  const dataSpacing = 2;
  const dataWidth = 48;
  const dataHeight = 48;

  const negativeDiagonals = DiagonalGridCalculator.calculateNegativeDiagonals(
    svgXScale,
    svgYScale,
    dataWidth,
    dataHeight,
    dataSpacing
  );

  svgPlotArea
    .append("g")
    .attr("class", "diagonal-grid-negative")
    .lower()
    .selectAll("line")
    .data(negativeDiagonals)
    .enter()
    .append("line")
    .attr("x1", (d: any) => d.x1)
    .attr("y1", (d: any) => d.y1)
    .attr("x2", (d: any) => d.x2)
    .attr("y2", (d: any) => d.y2)
    .style("stroke", "#666")
    .style("stroke-width", 0.8)
    .style("stroke-opacity", 0.6)
    .style("stroke-dasharray", "3,1");
}

// 渲染对角线网格（兼容性函数）
function renderDiagonalGrids(_plotArea?: any, _xScale?: any, _yScale?: any) {
  if (layerControls.value.showPositiveDiagonal) {
    renderPositiveDiagonal();
  }
  if (layerControls.value.showNegativeDiagonal) {
    renderNegativeDiagonal();
  }
}

// 处理数据更新
async function handleUpdateData() {
  try {
    await updateData();
    // 重新渲染图表
    d3.select("#chart").selectAll("*").remove();
    await initializeChart();
  } catch (err) {
    console.error("更新数据失败:", err);
  }
}

// 格式化更新时间
function formatUpdateTime(time: Date): string {
  return time.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// 图层控制函数
function toggleLayer(
  layerType: "grid" | "positiveDiagonal" | "negativeDiagonal"
) {
  if (!svgPlotArea) return;

  switch (layerType) {
    case "grid":
      if (layerControls.value.showGrid) {
        renderGrid();
      } else {
        // 只移除网格线，保留坐标轴
        svgPlotArea.selectAll(".x-grid, .y-grid").remove();
      }
      break;

    case "positiveDiagonal":
      if (layerControls.value.showPositiveDiagonal) {
        renderPositiveDiagonal();
      } else {
        svgPlotArea.selectAll(".diagonal-grid-positive").remove();
      }
      break;

    case "negativeDiagonal":
      if (layerControls.value.showNegativeDiagonal) {
        renderNegativeDiagonal();
      } else {
        svgPlotArea.selectAll(".diagonal-grid-negative").remove();
      }
      break;
  }
}

// 初始化图表
async function initializeChart() {
  // 等待数据加载
  await initializeData();

  // 重新执行原来的onMounted逻辑
  // Create SVG
  const svg = d3
    .select("#chart")
    .append("svg")
    .attr("width", width)
    .attr("height", height);

  // Create plot area
  const plotArea = svg
    .append("g")
    .attr("transform", `translate(${margin.left}, ${margin.top})`)
    .attr("preserveAspectRatio", "xMidYMid meet");
  svgPlotArea = plotArea; // 保存到全局变量

  // Coordinate point data
  const data = lottoData.value;
  // 创建x轴比例尺
  const xScale = d3.scaleLinear().domain([0, 48]).range([0, plotWidth]);
  svgXScale = xScale; // 保存到全局变量

  // 创建y轴比例尺
  const yScale = d3.scaleLinear().domain([0, 48]).range([plotHeight, 0]);
  svgYScale = yScale; // 保存到全局变量

  // 始终渲染坐标轴（刻度和标签）
  renderAxes();

  // 根据图层控制状态渲染网格线
  if (layerControls.value.showGrid) {
    renderGrid();
  }

  // Create coordinate points
  plotArea
    .append("g")
    .selectAll("circle")
    .data(data)
    .enter()
    .append("circle")
    .attr("cx", (d) => xScale(d.x))
    .attr("cy", (d) => yScale(d.y))
    .attr("r", 9)
    .attr("fill", (d) => d.color)
    .attr("class", (d) => `${d.clicked} point`)
    .style("opacity", (d) => d.opacity)
    .on("mouseover", function (_event, d) {
      // 坐标信息显示（独立于三角形计算）
      if (layerControls.value.showCoordinateInfo) {
        currentHoverPoint.value = d;
        handlePointHighlight(this, true);
      }

      // 三角形计算
      if (layerControls.value.showTriangleCalculation) {
        handleMouseOver(this, d);
        drawTriangleLines(plotArea, xScale, yScale);
      }
    })
    .on("mouseout", function () {
      // 清除坐标信息显示
      if (layerControls.value.showCoordinateInfo) {
        currentHoverPoint.value = null;
        handlePointHighlight(this, false);
      }

      // 清除三角形计算
      if (layerControls.value.showTriangleCalculation) {
        plotArea.select("#line-a").remove();
        plotArea.select("#line-b").remove();
        plotArea.select("#line-c").remove();
      }
      handleMouseOut(this);
    })
    .on("click", handleClick)
    .raise();

  // 添加文本标签显示历史数据点的数值
  plotArea
    .append("g")
    .selectAll("text")
    .data(lottoData2.value)
    .enter()
    .append("text")
    .text((d) => d.x.toString().padStart(2, "0"))
    .attr("x", (d) => xScale(d.x))
    .attr("y", (d) => yScale(d.y))
    .attr("text-anchor", "middle")
    .attr("alignment-baseline", "middle")
    .style("font-size", "10px")
    .style("fill", "white")
    .style("pointer-events", "none");

  // 根据图层控制状态渲染对角线网格
  renderDiagonalGrids();
}

// 处理点的高亮效果
function handlePointHighlight(element: any, highlight: boolean) {
  const selection = d3.select(element);
  if (highlight) {
    // 高亮效果：增大半径，添加边框
    selection
      .transition()
      .duration(150)
      .attr("r", 12)
      .style("stroke", "#fff")
      .style("stroke-width", 2);
  } else {
    // 恢复原始状态
    selection.transition().duration(150).attr("r", 9).style("stroke", "none");
  }
}

function handleMouseOver(element: any, d: Coordinate) {
  d3.select(element).style("opacity", "1");
  axisA.value = d;
  const [closestPoints, isIsosceles] = findNearest(lottoData2.value, d);
  axisB.value = closestPoints[0];
  axisC.value = closestPoints[1];

  // 存储是否为等腰三角形的状态
  window.isIsoscelesTriangle = isIsosceles;
  isIsoscelesTriangle.value = isIsosceles;
}

// 绘制三角形连线
function drawTriangleLines(plotArea: any, xScale: any, yScale: any) {
  if (!axisA.value || !axisB.value || !axisC.value) return;

  // 清除之前的线
  plotArea.select("#line-a").remove();
  plotArea.select("#line-b").remove();
  plotArea.select("#line-c").remove();

  const lineColor = isIsoscelesTriangle.value ? "green" : "yellow";

  // 绘制A-B线
  plotArea
    .append("g")
    .lower()
    .append("line")
    .attr("id", "line-a")
    .attr("x1", xScale(axisA.value.x))
    .attr("y1", yScale(axisA.value.y))
    .attr("x2", xScale(axisB.value.x))
    .attr("y2", yScale(axisB.value.y))
    .style("stroke", lineColor)
    .style("stroke-width", 2);

  // 绘制B-C线
  plotArea
    .append("g")
    .lower()
    .append("line")
    .attr("id", "line-b")
    .attr("x1", xScale(axisB.value.x))
    .attr("y1", yScale(axisB.value.y))
    .attr("x2", xScale(axisC.value.x))
    .attr("y2", yScale(axisC.value.y))
    .style("stroke", lineColor)
    .style("stroke-width", 2);

  // 绘制C-A线
  plotArea
    .append("g")
    .lower()
    .append("line")
    .attr("id", "line-c")
    .attr("x1", xScale(axisC.value.x))
    .attr("y1", yScale(axisC.value.y))
    .attr("x2", xScale(axisA.value.x))
    .attr("y2", yScale(axisA.value.y))
    .style("stroke", lineColor)
    .style("stroke-width", 2);
}
function handleMouseOut(element: any) {
  // 如果点没有被点击选中，则恢复原来的透明度
  if (!d3.select(element).classed("clicked")) {
    const data = d3.select(element).datum() as Coordinate;
    // 如果是选择区域的点（原始opacity为0），则隐藏
    if (data.opacity === 0) {
      d3.select(element).style("opacity", 0);
    }
  }
}
function handleClick(this: any) {
  const isClicked = !d3.select(this).classed("clicked");
  d3.select(this).classed("clicked", isClicked);

  // 获取当前点击的坐标点数据
  const clickedData = d3.select(this).datum() as Coordinate;

  // 显示或隐藏数值和圆点
  if (isClicked) {
    // 如果是选择区域的点（opacity为0），需要显示圆点
    if (clickedData.opacity === 0) {
      d3.select(this).style("opacity", 1).attr("fill", clickedData.color);
    }

    // 显示数值
    svgPlotArea
      .append("text")
      .attr("class", `text-for-${clickedData.x}-${clickedData.y}`)
      .text(clickedData.x.toString().padStart(2, "0"))
      .attr("x", svgXScale(clickedData.x))
      .attr("y", svgYScale(clickedData.y))
      .attr("text-anchor", "middle")
      .attr("alignment-baseline", "middle")
      .style("font-size", "10px")
      .style("fill", "black")
      .style("pointer-events", "none");
  } else {
    // 如果取消选中
    // 如果是选择区域的点，隐藏圆点
    if (clickedData.opacity === 0) {
      d3.select(this).style("opacity", 0);
    }

    // 移除数值
    svgPlotArea.select(`.text-for-${clickedData.x}-${clickedData.y}`).remove();
  }
}

// 原注释代码已移至上方实现
</script>

<style scoped>
/* 移除全局网格线样式，改为在JavaScript中动态控制 */

.diagonal-grid-positive line {
  stroke: #888;
  stroke-opacity: 0.6;
  stroke-width: 0.5;
  stroke-dasharray: 2, 2;
}

.diagonal-grid-negative line {
  stroke: #666;
  stroke-opacity: 0.6;
  stroke-width: 0.5;
  stroke-dasharray: 3, 1;
}

.selected {
  fill: blue;
}

#chart {
  overflow-y: scroll;
}

:fullscreen {
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

.point:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.g-hover {
  transform: rotate(90deg);
  /* border: #646cffaa 1px solid; */
}

.controls {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.update-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.update-btn:hover:not(:disabled) {
  background-color: #45a049;
}

.update-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.error-message {
  color: #ff4444;
  background-color: #ffebee;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 10px 0;
  border-left: 4px solid #ff4444;
}

.update-info {
  color: #666;
  font-size: 12px;
  margin: 5px 0;
}

/* 坐标信息显示 */
.coordinate-info {
  /* background-color: #e3f2fd; */
  padding: 6px 10px;
  border-radius: 4px;
  margin: 8px 0;
  font-family: monospace;
  font-size: 13px;
  border-left: 3px solid #1976d2;
  color: #0d47a1;
}

/* 三角形信息显示 */
.triangle-info {
  /* background-color: #f5f5f5; */
  padding: 8px 12px;
  border-radius: 4px;
  margin: 10px 0;
  font-family: monospace;
  font-size: 14px;
  border-left: 4px solid #2196f3;
}

.triangle-type {
  font-weight: bold;
  margin-left: 8px;
}

.triangle-type.isosceles {
  color: #4caf50;
}

/* 图层控制面板 - 暗色系主题 */
.layer-controls {
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 12px;
  margin: 10px 0;
  max-width: 400px;
}

.layer-controls h4 {
  margin: 0 0 10px 0;
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  min-width: 120px;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 6px;
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.checkbox-item span {
  font-size: 13px;
  color: #ccc;
}

.checkbox-item:hover span {
  color: #fff;
}

.checkbox-item input[type="checkbox"]:checked + span {
  color: #64b5f6;
  font-weight: 500;
}
</style>
