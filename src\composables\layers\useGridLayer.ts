import * as d3 from "d3";
import type { LayerManager } from "../useD3Visualization";

/**
 * 网格线图层管理器
 * 负责渲染可控制显示/隐藏的网格线
 */
export class GridLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private plotWidth: number;
  private plotHeight: number;
  private isVisible: boolean = false;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    plotWidth: number,
    plotHeight: number
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.plotWidth = plotWidth;
    this.plotHeight = plotHeight;
  }

  /**
   * 渲染网格线
   */
  render(): void {
    if (!this.isVisible) return;

    // 移除现有网格线
    this.plotArea.selectAll(".x-grid, .y-grid").remove();

    // 创建X轴网格线（延长的tick线）
    const xGrid = d3
      .axisBottom(this.xScale)
      .ticks(48)
      .tickSize(-this.plotHeight) // 延长为网格线
      .tickFormat(() => "") // 不显示标签
      .tickSizeOuter(0);

    // 创建Y轴网格线（延长的tick线）
    const yGrid = d3
      .axisLeft(this.yScale)
      .ticks(48)
      .tickSize(-this.plotWidth) // 延长为网格线
      .tickFormat(() => "") // 不显示标签
      .tickSizeOuter(0);

    // 渲染X轴网格线
    const xGridGroup = this.plotArea
      .append("g")
      .attr("class", "x-grid")
      .attr("transform", `translate(0, ${this.plotHeight})`)
      .call(xGrid);

    // 设置网格线样式 - 增强可见性
    xGridGroup
      .selectAll(".tick line")
      .style("stroke-width", 0.5)
      .style("stroke", "#999")
      .style("opacity", 0.6);

    // 隐藏坐标轴主线
    xGridGroup.select(".domain").style("display", "none");

    // 渲染Y轴网格线
    const yGridGroup = this.plotArea
      .append("g")
      .attr("class", "y-grid")
      .call(yGrid);

    // 设置网格线样式 - 增强可见性
    yGridGroup
      .selectAll(".tick line")
      .style("stroke-width", 0.5)
      .style("stroke", "#999")
      .style("opacity", 0.6);

    // 隐藏坐标轴主线
    yGridGroup.select(".domain").style("display", "none");
  }

  /**
   * 更新数据
   */
  update(_data: any): void {
    // 网格线不依赖数据，只需重新渲染
    this.render();
  }

  /**
   * 切换显示状态
   */
  toggle(visible: boolean): void {
    this.isVisible = visible;
    if (visible) {
      this.render();
    } else {
      this.destroy();
    }
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.plotArea.selectAll(".x-grid, .y-grid").remove();
  }
}

/**
 * 网格线图层组合函数
 */
export function useGridLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  plotWidth: number,
  plotHeight: number
) {
  const manager = new GridLayerManager(
    plotArea,
    xScale,
    yScale,
    plotWidth,
    plotHeight
  );

  return {
    manager,
  };
}
