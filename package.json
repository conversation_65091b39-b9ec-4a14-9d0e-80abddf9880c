{"name": "lotto_charts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/integrations": "^10.5.0", "axios": "^1.5.1", "d3": "^7.8.5", "vue": "^3.3.4"}, "devDependencies": {"@types/d3": "^7.4.1", "@types/node": "^20.8.2", "@vitejs/plugin-vue": "^4.2.3", "@vueuse/core": "^10.4.1", "typescript": "^5.0.2", "vite": "^4.4.5", "vue-tsc": "^1.8.5"}}