import * as d3 from 'd3';
import { DiagonalGridCalculator } from '../../utils/diagonalGrid';
import type { LayerManager } from '../useD3Visualization';

/**
 * 对角线图层管理器
 * 负责渲染正对角线和负对角线
 */
export class DiagonalLayerManager implements LayerManager {
  private plotArea: d3.Selection<SVGGElement, unknown, null, undefined>;
  private xScale: d3.ScaleLinear<number, number>;
  private yScale: d3.ScaleLinear<number, number>;
  private plotWidth: number;
  private plotHeight: number;
  private showPositive: boolean = false;
  private showNegative: boolean = false;

  constructor(
    plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
    xScale: d3.ScaleLinear<number, number>,
    yScale: d3.ScaleLinear<number, number>,
    plotWidth: number,
    plotHeight: number
  ) {
    this.plotArea = plotArea;
    this.xScale = xScale;
    this.yScale = yScale;
    this.plotWidth = plotWidth;
    this.plotHeight = plotHeight;
  }

  /**
   * 渲染正对角线
   */
  private renderPositiveDiagonal(): void {
    if (!this.showPositive) return;

    // 移除现有正对角线
    this.plotArea.selectAll('.diagonal-grid-positive').remove();

    const dataSpacing = 2;
    const dataWidth = 48;
    const dataHeight = 48;

    const positiveDiagonals = DiagonalGridCalculator.calculatePositiveDiagonals(
      this.xScale,
      this.yScale,
      dataWidth,
      dataHeight,
      dataSpacing
    );

    this.plotArea
      .append('g')
      .attr('class', 'diagonal-grid-positive')
      .lower()
      .selectAll('line')
      .data(positiveDiagonals)
      .enter()
      .append('line')
      .attr('x1', (d: any) => d.x1)
      .attr('y1', (d: any) => d.y1)
      .attr('x2', (d: any) => d.x2)
      .attr('y2', (d: any) => d.y2)
      .style('stroke', '#888')
      .style('stroke-width', 0.5)
      .style('stroke-opacity', 0.6)
      .style('stroke-dasharray', '2, 2');
  }

  /**
   * 渲染负对角线
   */
  private renderNegativeDiagonal(): void {
    if (!this.showNegative) return;

    // 移除现有负对角线
    this.plotArea.selectAll('.diagonal-grid-negative').remove();

    const dataSpacing = 2;
    const dataWidth = 48;
    const dataHeight = 48;

    const negativeDiagonals = DiagonalGridCalculator.calculateNegativeDiagonals(
      this.xScale,
      this.yScale,
      dataWidth,
      dataHeight,
      dataSpacing
    );

    this.plotArea
      .append('g')
      .attr('class', 'diagonal-grid-negative')
      .lower()
      .selectAll('line')
      .data(negativeDiagonals)
      .enter()
      .append('line')
      .attr('x1', (d: any) => d.x1)
      .attr('y1', (d: any) => d.y1)
      .attr('x2', (d: any) => d.x2)
      .attr('y2', (d: any) => d.y2)
      .style('stroke', '#666')
      .style('stroke-width', 0.5)
      .style('stroke-opacity', 0.6)
      .style('stroke-dasharray', '3, 1');
  }

  /**
   * 渲染所有对角线
   */
  render(): void {
    this.renderPositiveDiagonal();
    this.renderNegativeDiagonal();
  }

  /**
   * 更新数据
   */
  update(_data: any): void {
    // 对角线不依赖数据，只需重新渲染
    this.render();
  }

  /**
   * 切换正对角线显示状态
   */
  togglePositive(visible: boolean): void {
    this.showPositive = visible;
    if (visible) {
      this.renderPositiveDiagonal();
    } else {
      this.plotArea.selectAll('.diagonal-grid-positive').remove();
    }
  }

  /**
   * 切换负对角线显示状态
   */
  toggleNegative(visible: boolean): void {
    this.showNegative = visible;
    if (visible) {
      this.renderNegativeDiagonal();
    } else {
      this.plotArea.selectAll('.diagonal-grid-negative').remove();
    }
  }

  /**
   * 切换显示状态（兼容LayerManager接口）
   */
  toggle(visible: boolean): void {
    // 这个方法用于整体控制，实际使用中应该分别控制正负对角线
    this.togglePositive(visible);
    this.toggleNegative(visible);
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.plotArea.selectAll('.diagonal-grid-positive, .diagonal-grid-negative').remove();
  }
}

/**
 * 对角线图层组合函数
 */
export function useDiagonalLayer(
  plotArea: d3.Selection<SVGGElement, unknown, null, undefined>,
  xScale: d3.ScaleLinear<number, number>,
  yScale: d3.ScaleLinear<number, number>,
  plotWidth: number,
  plotHeight: number
) {
  const manager = new DiagonalLayerManager(plotArea, xScale, yScale, plotWidth, plotHeight);

  return {
    manager,
    togglePositive: (visible: boolean) => manager.togglePositive(visible),
    toggleNegative: (visible: boolean) => manager.toggleNegative(visible),
  };
}
