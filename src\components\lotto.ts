// import * as fs from 'fs';
import { useAxios } from "@vueuse/integrations/useAxios";

interface Coordinate {
  x: number;
  y: number;
  color: string;
  opacity: 0 | 1;
  display: "none" | "block";
  visibility: "hidden" | "visible";
  clicked: "clicked" | "";
}

// function convertCsvToResult(filePath: string): number[][] {
//   const csv = fs.readFileSync(filePath, 'utf-8');
//   const lines = csv.split("\n");
//   const result: number[][] = [];

//   for (let line of lines) {
//     const values = line.split(",");
//     const row: number[] = [];

//     for (let i = 0; i < values.length; i++) {
//       // 将每个值转换为正整数
//       if (i === 0) {
//         row.push(parseInt(values[i].replace(/"/g, "")));
//       } else {
//         row.push(parseInt(values[i]));
//       }
//     }

//     // 将最后两列的数加上35
//     row[row.length - 2] += 35;
//     row[row.length - 1] += 35;

//     result.push(row);
//   }

//   return result;
// }

interface ResData {
  code: number;
  message: string;
  data: [];
}

import csv from "./lotto-20250406";

// get csv data
async function get_csv_data(): Promise<string[][]> {
  // const { data } = await useAxios<ResData>(
  //   "http://127.0.0.1:5800/api/v1/lotto/csv"
  // );
  // if (data) {
  //   // let d = unref(indirectRef.value?.data);
  //   if (data.value?.data !== undefined) {
  //     return data.value.data;
  //   }
  //   // console.log(data.value?.data);
  // }
  // return [];
  const d = csv.split("\n").map((line) => line.split(","));
  // console.log(d);
  return d;
}

let per: number = 0;

/**
 * Converts a CSV string into a two-dimensional array of numbers.
 *
 * @return {number[][]} The converted two-dimensional array of numbers.
 */
async function convertCsvToResult(): Promise<number[][]> {
  const result: number[][] = [];

  await get_csv_data().then((data) => {
    per = parseInt(data[0][0].replace(/"/g, '')); // 使用正则表达式移除字符串中的所有双引号，然后将结果转换为整数
    for (let line of data.slice(0, 38)) {
      const row: number[] = [];
      for (let i = 0; i < line.length; i++) {
        row.push(parseInt(line[i].replace(/"/g, ''), 10));
      }
      // 将最后两列的数加上35
      row[row.length - 2] += 35;
      row[row.length - 1] += 35;
      result.push(row);
    }
  });

  //   for (let line of lines) {
  //     const values = line.split(",");
  //     const row: number[] = [];

  //     for (let i = 0; i < values.length; i++) {
  //       // 将每个值转换为正整数
  //       // if (i === 0) {
  //       //     row.push(parseInt(values[i]));
  //       // } else {
  //       //     row.push(parseInt(values[i]));
  //       // }
  //       row.push(parseInt(values[i].replace(/"/g, "")));
  //     }

  //     // 将最后两列的数加上35
  //     row[row.length - 2] += 35;
  //     row[row.length - 1] += 35;

  //     result.push(row);
  //   }
  console.log("per",per);
  console.log("转换后的",result);
  return result;
}
/**
 * Calculates the coordinates and colors based on the provided list of numbers.
 *
 * @param {number[][]} list1 - The list of numbers.
 * @return {[Coordinate[], Coordinate[]]} - An array of two lists containing coordinates and colors.
 */
function calculateCoordinatesAndColors(
  list1: number[][]
): [Coordinate[], Coordinate[]] {
  const colors = ["#adb5bd", "#f06595", "#51cf66", "#ffe066", "#f76707"];
  let list2: Coordinate[] = [];

  // 计算每个数的坐标和颜色
  for (let i = 0; i < list1.length; i++) {
    const row = list1[i];

    for (let j = 1; j < row.length; j++) {
      const x = row[j];
      const y = i + 11;
      const colorIndex = x % 5;
      const color = colors[colorIndex > 0 ? colorIndex - 1 : 4];

      list2.push({
        x,
        y,
        color,
        opacity: 1,
        display: "block",
        visibility: "visible",
        clicked: "clicked",
      });
    }
  }
  const list3 = list2.concat(); // 复制一份
  // 计算y轴为1到10的坐标和颜色
  for (let y = 1; y <= 10; y++) {
    for (let x = 1; x <= 47; x++) {
      const colorIndex = x % 5;
      const color = colors[colorIndex > 0 ? colorIndex - 1 : 4];

      list3.push({
        x,
        y,
        color,
        opacity: 0,
        display: "none",
        visibility: "hidden",
        clicked: "",
      });
    }
  }
  return [list3, list2];
}

const result = await convertCsvToResult().then((res) => {
  return res;
});
const Data = calculateCoordinatesAndColors(result);

// 返回最近的两个点的坐标及是否形成等腰三角形
function findNearest(points: Coordinate[], pointA: Coordinate): [Coordinate[], boolean] {
  // 计算每个点到点A的距离
  var distances = points.map(function (point, index) {
    var dx = point.x - pointA.x; // 计算点A到当前点的x轴距离
    var dy = point.y - pointA.y; // 计算点A到当前点的y轴距离
    return {
      value: Math.sqrt(dx * dx + dy * dy), //距离
      index: index, // 索引
    }; // 计算点A到当前点的欧氏距离
  });

  // 找到距离A点最近的两个点的索引
  var sortedDistances = [...distances]; // 创建排序后的距离数组副本
  sortedDistances.sort((a, b) => a.value - b.value); // 按升序对副本进行排序

  //   console.log("距离", sortedDistances.slice(0, 10));
  var minIndices: number[] = [];
  minIndices.push(sortedDistances[1].index); // 从第二个开始 排除距离为0的自身

  if (sortedDistances[1] === sortedDistances[2]) {
    minIndices.push(sortedDistances[3].index);
  } else {
    minIndices.push(sortedDistances[2].index);
  }

  //   console.log(minIndices);
  // 获取最近的两个点
  var closestPoints = minIndices.map(function (index) {
    return points[index]; // 根据索引获取最近的两个点
  });

  // 判断三点是否形成等腰三角形
  const pointB = closestPoints[0];
  const pointC = closestPoints[1];
  
  // 计算三边长度
  const distAB = Math.sqrt(Math.pow(pointA.x - pointB.x, 2) + Math.pow(pointA.y - pointB.y, 2));
  const distBC = Math.sqrt(Math.pow(pointB.x - pointC.x, 2) + Math.pow(pointB.y - pointC.y, 2));
  const distCA = Math.sqrt(Math.pow(pointC.x - pointA.x, 2) + Math.pow(pointC.y - pointA.y, 2));
  
  // 判断是否为等腰三角形（两边相等）
  // 使用误差范围进行比较，因为浮点数计算可能有微小误差
  const epsilon = 0.001; // 误差容差
  const isIsosceles = 
    Math.abs(distAB - distBC) < epsilon || 
    Math.abs(distBC - distCA) < epsilon || 
    Math.abs(distCA - distAB) < epsilon;
  
  return [closestPoints, isIsosceles];
}

// 纵向最近的点
function findClosestPoint(
  coordinates: Coordinate[],
  pointA: Coordinate
): Coordinate {
  // 初始化最小距离和最近的点B的坐标
  let minDistance = Infinity;
  let closestPoint: Coordinate = {
    x: 0,
    y: 0,
    color: "",
    clicked: "",
    opacity: 0,
    display: "none",
    visibility: "hidden",
  };

  // 遍历坐标数组
  coordinates.forEach((pointB) => {
    // 确保点B的Y大于点A的Y
    if (pointB.y > pointA.y) {
      // 计算点A和点B之间的纵向距离
      const distance = Math.abs(pointB.y - pointA.y);
      // 计算点A和点B之间的横向距离
      const horizontalDistance = Math.abs(pointB.x - pointA.x);

      // 如果距离比当前最小距离小，则更新最小距离和最近的点B的坐标
      if (distance < minDistance && horizontalDistance === distance) {
        minDistance = distance;
        closestPoint = pointB;
      }
    }
  });

  return closestPoint;
}

const lottoData = Data[0];
const lottoData2 = Data[1];

const csv_data = get_csv_data();

export {
  lottoData,
  lottoData2,
  findNearest,
  csv_data,
  findClosestPoint,
  type Coordinate,
  per,
};
