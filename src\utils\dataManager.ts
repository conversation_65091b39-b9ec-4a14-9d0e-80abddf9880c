import axios from 'axios';

interface DataCache {
  data: string;
  timestamp: number;
  version: string;
}

export class DataManager {
  private static readonly STORAGE_KEY = 'lotto_data_cache';
  private static readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存
  private static readonly API_URL = '/api/lottery/downLottoData';

  /**
   * 获取彩票数据 - 优先使用缓存，缓存过期或不存在时从API获取
   */
  static async getData(): Promise<string> {
    try {
      // 首先检查缓存
      const cachedData = this.getCachedData();
      if (cachedData && !this.isDataExpired()) {
        console.log('使用缓存数据');
        return cachedData.data;
      }

      // 缓存过期或不存在，从API获取
      console.log('从API获取数据');
      return await this.fetchFromAPI();
    } catch (error) {
      console.error('获取数据失败:', error);
      
      // API失败时尝试使用过期缓存
      const cachedData = this.getCachedData();
      if (cachedData) {
        console.log('API失败，使用过期缓存数据');
        return cachedData.data;
      }
      
      // 如果连缓存都没有，抛出错误
      throw new Error('无法获取数据：API请求失败且无可用缓存');
    }
  }

  /**
   * 强制更新数据 - 忽略缓存，直接从API获取最新数据
   */
  static async updateData(): Promise<string> {
    try {
      console.log('强制更新数据');
      const data = await this.fetchFromAPI();
      return data;
    } catch (error) {
      console.error('更新数据失败:', error);
      throw new Error(`数据更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从API获取数据并缓存
   */
  private static async fetchFromAPI(): Promise<string> {
    try {
      const response = await axios.get(this.API_URL, {
        timeout: 15000,
        responseType: 'text',
        headers: {
          'Accept': 'text/plain, text/csv, */*'
        }
      });

      if (!response.data || typeof response.data !== 'string') {
        throw new Error('API返回数据格式错误');
      }

      // 简单验证数据格式
      if (!this.validateDataFormat(response.data)) {
        throw new Error('API返回数据格式不符合预期');
      }

      // 缓存数据
      this.cacheData(response.data);
      
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error('请求超时，请检查网络连接');
        } else if (error.response?.status === 404) {
          throw new Error('数据源不存在');
        } else if (error.response?.status >= 500) {
          throw new Error('服务器错误，请稍后重试');
        }
      }
      throw error;
    }
  }

  /**
   * 验证数据格式
   */
  private static validateDataFormat(data: string): boolean {
    const lines = data.trim().split('\n');
    if (lines.length < 10) return false; // 至少要有10期数据
    
    // 检查第一行格式
    const firstLine = lines[0];
    const pattern = /^"[0-9]+",("[0-9]{2}",){6}"[0-9]{2}"$/;
    return pattern.test(firstLine);
  }

  /**
   * 缓存数据到localStorage
   */
  private static cacheData(data: string): void {
    try {
      const cache: DataCache = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache));
      console.log('数据已缓存到localStorage');
    } catch (error) {
      console.warn('缓存数据失败:', error);
      // 缓存失败不影响主流程
    }
  }

  /**
   * 从localStorage获取缓存数据
   */
  private static getCachedData(): DataCache | null {
    try {
      const cached = localStorage.getItem(this.STORAGE_KEY);
      if (!cached) return null;
      
      const data = JSON.parse(cached) as DataCache;
      return data;
    } catch (error) {
      console.warn('读取缓存失败:', error);
      return null;
    }
  }

  /**
   * 检查缓存是否过期
   */
  static isDataExpired(): boolean {
    const cached = this.getCachedData();
    if (!cached) return true;
    
    return Date.now() - cached.timestamp > this.CACHE_DURATION;
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('缓存已清除');
    } catch (error) {
      console.warn('清除缓存失败:', error);
    }
  }

  /**
   * 获取缓存信息
   */
  static getCacheInfo(): { exists: boolean; timestamp?: number; expired?: boolean } {
    const cached = this.getCachedData();
    if (!cached) {
      return { exists: false };
    }
    
    return {
      exists: true,
      timestamp: cached.timestamp,
      expired: this.isDataExpired()
    };
  }
}
