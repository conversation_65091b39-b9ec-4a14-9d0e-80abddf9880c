<template>
  <h3>Lotto D3 - 重构版本</h3>
  <div id="chart-refactored" ref="chartContainer"></div>
  <br />

  <!-- 坐标信息显示 -->
  <div
    v-if="currentHoverPoint && visualization?.layerControls.showCoordinateInfo"
    class="coordinate-info"
  >
    <span
      >当前坐标: ({{ currentHoverPoint.x }}, {{ currentHoverPoint.y }})</span
    >
  </div>

  <!-- 三角形计算信息显示 -->
  <div
    v-if="
      visualization?.layerControls.showTriangleCalculation &&
      (axisA || axisB || axisC)
    "
    class="triangle-info"
  >
    <span v-if="axisA && axisB && axisC">
      A(x:{{ axisA.x }}, y:{{ axisA.y }}) B(x:{{ axisB.x }}, y:{{ axisB.y }})
      C(x:{{ axisC.x }}, y:{{ axisC.y }})
      <span class="triangle-type" :class="{ isosceles: isIsoscelesTriangle }">
        {{ isIsoscelesTriangle ? "[等腰三角形]" : "[普通三角形]" }}
      </span>
    </span>
  </div>

  <br />

  <!-- 控制面板 -->
  <div class="controls">
    <button @click="toggle">全屏</button>
    <button @click="handleUpdateData" :disabled="isLoading" class="update-btn">
      {{ isLoading ? "更新中..." : "更新数据" }}
    </button>
  </div>

  <!-- 图层控制面板 -->
  <div class="layer-controls" v-if="visualization">
    <h4>图层控制</h4>
    <div class="checkbox-group">
      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showGrid"
          @change="toggleLayer('showGrid')"
        />
        <span>显示网格线</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showPositiveDiagonal"
          @change="toggleLayer('showPositiveDiagonal')"
        />
        <span>显示正对角线</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showNegativeDiagonal"
          @change="toggleLayer('showNegativeDiagonal')"
        />
        <span>显示负对角线</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showCoordinateInfo"
        />
        <span>显示坐标信息</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showTriangleCalculation"
        />
        <span>显示三角形计算</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.enableLineDrawing"
          @change="toggleLayer('enableLineDrawing')"
        />
        <span>启用连线绘制</span>
      </label>

      <label class="checkbox-item">
        <input
          type="checkbox"
          v-model="visualization.layerControls.showLineDrawing"
          @change="toggleLayer('showLineDrawing')"
        />
        <span>显示已绘制连线</span>
      </label>
    </div>

    <!-- 连线绘制控制按钮 -->
    <div
      v-if="visualization.layerControls.enableLineDrawing"
      class="line-drawing-controls"
    >
      <h5>连线绘制控制</h5>
      <button @click="clearAllLines" class="control-btn">清除所有连线</button>
      <button @click="undoLastLine" class="control-btn">撤销上一条连线</button>
    </div>
  </div>

  <!-- 数据信息 -->
  <div class="data-info">
    <p>当前期号: {{ currentPer }}</p>
    <p v-if="lastUpdateTime">
      最后更新: {{ formatUpdateTime(lastUpdateTime) }}
    </p>
    <p v-if="error" class="error">错误: {{ error }}</p>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { useFullscreen } from "@vueuse/core";
import { useLottoData } from "../composables/useLottoData";
import { useD3Visualization } from "../composables/useD3Visualization";
import { useAxisLayer } from "../composables/layers/useAxisLayer";
import { useGridLayer } from "../composables/layers/useGridLayer";
import { useDiagonalLayer } from "../composables/layers/useDiagonalLayer";
import { useDataPointsLayer } from "../composables/layers/useDataPointsLayer";
import { useTriangleLayer } from "../composables/layers/useTriangleLayer";
import { useLineDrawingLayer } from "../composables/layers/useLineDrawingLayer";

import type { Coordinate } from "../composables/useLottoData";

// 使用彩票数据组合式函数
const {
  lottoData,
  lottoData2,
  currentPer,
  isLoading,
  error,
  lastUpdateTime,
  updateData,
  initializeData,
} = useLottoData();

// 容器引用
const chartContainer = ref<HTMLElement | null>(null);

// 三角形状态
const axisA = ref<Coordinate>();
const axisB = ref<Coordinate>();
const axisC = ref<Coordinate>();
const isIsoscelesTriangle = ref(false);

// 当前悬停点信息
const currentHoverPoint = ref<Coordinate | null>(null);

const { toggle } = useFullscreen(chartContainer as any);

// 可视化配置
const visualizationConfig = {
  width: 1300,
  height: 1300,
  margin: { top: 20, right: 20, bottom: 35, left: 35 },
};

// D3可视化组合函数
const visualization = ref<any>(null);
let axisLayer: ReturnType<typeof useAxisLayer> | null = null;
let gridLayer: ReturnType<typeof useGridLayer> | null = null;
let diagonalLayer: ReturnType<typeof useDiagonalLayer> | null = null;
let dataPointsLayer: ReturnType<typeof useDataPointsLayer> | null = null;
let triangleLayer: ReturnType<typeof useTriangleLayer> | null = null;
let lineDrawingLayer: ReturnType<typeof useLineDrawingLayer> | null = null;

/**
 * 初始化可视化系统
 */
function initializeVisualization() {
  if (!chartContainer.value || visualization.value) return;

  // 初始化核心可视化系统
  visualization.value = useD3Visualization(
    chartContainer.value,
    visualizationConfig,
    {
      onPointHover: (point) => {
        currentHoverPoint.value = point;
      },
      onPointClick: (point) => {
        console.log("Point clicked:", point);
      },
      onTriangleUpdate: (a, b, c, isIsosceles) => {
        axisA.value = a;
        axisB.value = b;
        axisC.value = c;
        isIsoscelesTriangle.value = isIsosceles;
      },
    }
  );

  // 获取D3上下文
  const context = visualization.value.getD3Context();
  if (!context.plotArea || !context.xScale || !context.yScale) return;

  // 初始化各个图层
  axisLayer = useAxisLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  gridLayer = useGridLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  diagonalLayer = useDiagonalLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    context.plotWidth,
    context.plotHeight
  );

  dataPointsLayer = useDataPointsLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    {
      onPointClick: (point) => {
        // 连线绘制优先级更高
        if (
          visualization.value?.layerControls.enableLineDrawing &&
          lineDrawingLayer
        ) {
          lineDrawingLayer.handlePointClick(point);
        } else if (
          visualization.value?.layerControls.showTriangleCalculation &&
          triangleLayer
        ) {
          triangleLayer.calculateTriangle(point);
        }
      },
      onPointHover: (point) => {
        currentHoverPoint.value = point;
      },
    }
  );

  triangleLayer = useTriangleLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    {
      onTriangleUpdate: (a, b, c, isIsosceles) => {
        axisA.value = a;
        axisB.value = b;
        axisC.value = c;
        isIsoscelesTriangle.value = isIsosceles;
      },
    }
  );

  lineDrawingLayer = useLineDrawingLayer(
    context.plotArea,
    context.xScale,
    context.yScale,
    {
      onPointClick: (point) => {
        console.log("Line drawing point clicked:", point);
      },
    }
  );

  // 注册图层到可视化系统
  if (visualization.value) {
    visualization.value.registerLayer("axis", axisLayer.manager);
    visualization.value.registerLayer("showGrid", gridLayer.manager);
    visualization.value.registerLayer(
      "showPositiveDiagonal",
      diagonalLayer.manager
    );
    visualization.value.registerLayer(
      "showNegativeDiagonal",
      diagonalLayer.manager
    );
    visualization.value.registerLayer(
      "showCoordinateInfo",
      dataPointsLayer.manager
    );
    visualization.value.registerLayer(
      "showTriangleCalculation",
      triangleLayer.manager
    );
    visualization.value.registerLayer(
      "showLineDrawing",
      lineDrawingLayer.manager
    );
  }

  // 初始渲染
  renderAllLayers();
}

/**
 * 更新所有图层数据
 */
function updateAllLayersData() {
  if (!visualization.value || !axisLayer || !dataPointsLayer || !triangleLayer)
    return;

  // 更新坐标轴期号
  axisLayer.updateCurrentPer(currentPer.value);

  // 更新数据点
  dataPointsLayer.updateData(lottoData.value, lottoData2.value);

  // 更新三角形计算的历史数据
  triangleLayer.updateHistoryData(lottoData2.value);

  // 更新所有图层
  const data = {
    currentPer: currentPer.value,
    allData: lottoData.value,
    historyData: lottoData2.value,
  };
  visualization.value.updateAllLayers(data);
}

/**
 * 渲染所有图层
 */
function renderAllLayers() {
  if (!visualization.value) return;
  visualization.value.renderAllLayers();
}

/**
 * 图层控制函数
 */
function toggleLayer(layerType: string) {
  if (!visualization.value) return;

  switch (layerType) {
    case "showGrid":
      visualization.value.toggleLayer("showGrid");
      break;
    case "showPositiveDiagonal":
      if (diagonalLayer) {
        diagonalLayer.togglePositive(
          visualization.value.layerControls.showPositiveDiagonal
        );
      }
      break;
    case "showNegativeDiagonal":
      if (diagonalLayer) {
        diagonalLayer.toggleNegative(
          visualization.value.layerControls.showNegativeDiagonal
        );
      }
      break;
    case "showCoordinateInfo":
      visualization.value.toggleLayer("showCoordinateInfo");
      break;
    case "showTriangleCalculation":
      visualization.value.toggleLayer("showTriangleCalculation");
      break;
    case "showLineDrawing":
      visualization.value.toggleLayer("showLineDrawing");
      break;
    case "enableLineDrawing":
      if (lineDrawingLayer) {
        lineDrawingLayer.setEnabled(
          visualization.value.layerControls.enableLineDrawing
        );
      }
      // 同时设置数据点图层的连线绘制模式
      if (dataPointsLayer) {
        dataPointsLayer.setLineDrawingMode(
          visualization.value.layerControls.enableLineDrawing
        );
      }
      break;
  }
}

// 数据更新处理函数
async function handleUpdateData() {
  await updateData();
  updateAllLayersData();
}

// 连线绘制控制函数
function clearAllLines() {
  if (lineDrawingLayer) {
    lineDrawingLayer.clearAllLines();
  }
}

function undoLastLine() {
  if (lineDrawingLayer) {
    lineDrawingLayer.undoLastLine();
  }
}

// 格式化更新时间
function formatUpdateTime(time: Date): string {
  return time.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

onMounted(async () => {
  // 初始化数据
  await initializeData();

  // 初始化可视化系统
  initializeVisualization();

  // 更新所有图层数据
  updateAllLayersData();
});

// 监听数据变化，自动更新图层
watch([lottoData, lottoData2, currentPer], () => {
  updateAllLayersData();
});
</script>

<style scoped>
/* 样式保持与原版本一致 */
.coordinate-info {
  /* background: #f0f0f0; */
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
}

.triangle-info {
  /* background: #e8f5e8; */
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
}

.triangle-type.isosceles {
  color: green;
  font-weight: bold;
}

.controls {
  margin: 16px 0;
}

.update-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.layer-controls {
  /* background: #f9f9f9; */
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

.checkbox-group {
  display: flex;
  /* flex-direction: column; */
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.data-info {
  margin: 16px 0;
  font-size: 14px;
}

.error {
  color: red;
}

.line-drawing-controls {
  margin-top: 16px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.line-drawing-controls h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.control-btn {
  margin-right: 8px;
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.control-btn:hover {
  background: #0056b3;
}

.control-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
